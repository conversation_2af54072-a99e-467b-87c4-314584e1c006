/**
 * 软体组件
 * 基于粒子和约束实现软体物理
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { PhysicsMaterialFactory } from '../PhysicsMaterialFactory';

/**
 * 软体类型
 */
export enum SoftBodyType {
  /** 布料 */
  CLOTH = 'cloth',
  /** 绳索 */
  ROPE = 'rope',
  /** 体积软体 */
  VOLUME = 'volume',
  /** 气球 */
  BALLOON = 'balloon',
  /** 果冻 */
  JELLY = 'jelly'
}

/**
 * 软体约束
 */
interface SoftBodyConstraint {
  /** 粒子索引A */
  particleA: number;
  /** 粒子索引B */
  particleB: number;
  /** 休息长度 */
  restLength: number;
  /** 刚度 */
  stiffness: number;
}

/**
 * 软体组件选项
 */
export interface SoftBodyComponentOptions {
  /** 软体类型 */
  type: SoftBodyType;
  /** 质量 */
  mass?: number;
  /** 刚度 */
  stiffness?: number;
  /** 阻尼 */
  damping?: number;
  /** 是否固定角落（仅布料） */
  fixedCorners?: boolean;
  /** 是否固定边缘（仅绳索） */
  fixedEnds?: boolean;
  /** 网格对象（可选） */
  mesh?: THREE.Mesh;
  /** 材质名称 */
  materialName?: string;
  /** 自定义参数 */
  params?: any;
}

/**
 * 软体组件
 */
export class SoftBodyComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'SoftBodyComponent';

  /** 软体类型 */
  private softBodyType: SoftBodyType;

  /** 粒子质量 */
  private mass: number;

  /** 刚度 */
  private stiffness: number;

  /** 阻尼 */
  private damping: number;

  /** 是否固定角落（仅布料） */
  private fixedCorners: boolean;

  /** 是否固定边缘（仅绳索） */
  private fixedEnds: boolean;

  /** 物理材质 */
  private material: CANNON.Material | null;

  /** 网格对象 */
  private mesh: THREE.Mesh | THREE.Line | null = null;

  /** 粒子物理体数组 */
  private particles: CANNON.Body[] = [];

  /** 约束数组 */
  private constraints: SoftBodyConstraint[] = [];

  /** 物理约束数组 */
  private physicsConstraints: CANNON.Constraint[] = [];

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 自定义参数 */
  private params: any;

  /** 物理世界引用 */
  private world: CANNON.World | null = null;

  /**
   * 创建软体组件
   * @param options 软体组件选项
   */
  constructor(options: SoftBodyComponentOptions) {
    super(SoftBodyComponent.type);

    this.softBodyType = options.type;
    this.mass = options.mass || 1;
    this.stiffness = options.stiffness || 100;
    this.damping = options.damping || 0.1;
    this.fixedCorners = options.fixedCorners || false;
    this.fixedEnds = options.fixedEnds || false;
    this.mesh = options.mesh || null;
    this.material = options.materialName ?
      PhysicsMaterialFactory.getMaterial(options.materialName) :
      null;
    this.params = options.params || {};
  }

  /**
   * 初始化软体
   * @param world 物理世界
   */
  public initialize(world: CANNON.World): void {
    if (this.initialized || !this.entity) return;

    this.world = world;

    // 根据软体类型创建不同的软体
    switch (this.softBodyType) {
      case SoftBodyType.CLOTH:
        this.createCloth();
        break;
      case SoftBodyType.ROPE:
        this.createRope();
        break;
      case SoftBodyType.VOLUME:
        this.createVolumeSoftBody();
        break;
      case SoftBodyType.BALLOON:
        this.createBalloon();
        break;
      case SoftBodyType.JELLY:
        this.createJelly();
        break;
      default:
        console.warn(`不支持的软体类型: ${this.softBodyType}`);
        return;
    }

    // 创建物理约束
    this.createPhysicsConstraints();

    this.initialized = true;
  }

  /**
   * 获取软体类型
   * @returns 软体类型
   */
  public getType(): SoftBodyType {
    return this.softBodyType;
  }

  /**
   * 获取粒子数组
   * @returns 粒子数组
   */
  public getParticles(): CANNON.Body[] | null {
    if (!this.initialized) return null;
    return this.particles;
  }

  /**
   * 应用外力到所有粒子
   * @param force 力向量
   */
  public applyForce(force: THREE.Vector3): void {
    if (!this.initialized) return;

    const cannonForce = new CANNON.Vec3(force.x, force.y, force.z);

    for (const particle of this.particles) {
      particle.applyForce(cannonForce, particle.position);
    }
  }

  /**
   * 应用冲量到所有粒子
   * @param impulse 冲量向量
   */
  public applyImpulse(impulse: THREE.Vector3): void {
    if (!this.initialized) return;

    const cannonImpulse = new CANNON.Vec3(impulse.x, impulse.y, impulse.z);

    for (const particle of this.particles) {
      particle.applyImpulse(cannonImpulse, particle.position);
    }
  }

  /**
   * 获取约束数组
   * @returns 约束数组
   */
  public getConstraints(): SoftBodyConstraint[] | null {
    if (!this.initialized) return null;
    return this.constraints;
  }

  /**
   * 移除约束
   * @param constraintIndices 约束索引数组
   * @returns 是否成功移除
   */
  public removeConstraints(constraintIndices: number[]): boolean {
    if (!this.initialized || !this.world) return false;

    // 按索引从大到小排序，以便从后向前移除
    constraintIndices.sort((a, b) => b - a);

    // 移除物理约束
    for (const index of constraintIndices) {
      if (index >= 0 && index < this.physicsConstraints.length) {
        const constraint = this.physicsConstraints[index];
        this.world.removeConstraint(constraint);

        // 从数组中移除
        this.physicsConstraints.splice(index, 1);
        this.constraints.splice(index, 1);
      }
    }

    return constraintIndices.length > 0;
  }

  /**
   * 设置参数
   * @param name 参数名
   * @param value 参数值
   */
  public setParameter(name: string, value: any): void {
    if (!this.initialized) return;

    this.params[name] = value;

    // 特殊处理某些参数
    if (name === 'pressure' && this.softBodyType === SoftBodyType.BALLOON) {
      // 更新气球压力
      this.params.pressure = value;
    }
  }

  /**
   * 创建布料
   */
  private createCloth(): void {
    // 从参数中获取网格大小
    const gridSize = this.params.gridSize || { x: 10, y: 10 };
    const width = this.params.width || 10;
    const height = this.params.height || 10;
    const segmentsX = gridSize.x;
    const segmentsY = gridSize.y;

    // 如果没有提供网格，创建一个平面网格
    if (!this.mesh) {
      const geometry = new THREE.PlaneGeometry(width, height, segmentsX, segmentsY);
      const material = new THREE.MeshStandardMaterial({
        color: 0x3366cc,
        side: THREE.DoubleSide,
        wireframe: false
      });
      this.mesh = new THREE.Mesh(geometry, material);
      // TODO: 需要创建正确的 MeshComponent 实例
      // this.entity?.addComponent(new MeshComponent({ mesh: this.mesh }));
    }

    // 创建粒子
    const stepX = width / segmentsX;
    const stepY = height / segmentsY;
    const particleMass = this.mass / (segmentsX * segmentsY);

    // 获取实体的变换
    const transform = this.entity?.getTransform();
    const position = transform ? transform.getPosition() : new THREE.Vector3();
    const rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();

    // 创建粒子网格
    for (let y = 0; y <= segmentsY; y++) {
      for (let x = 0; x <= segmentsX; x++) {
        // 计算粒子位置
        const px = (x / segmentsX - 0.5) * width;
        const py = 0;
        const pz = (y / segmentsY - 0.5) * height;

        // 创建粒子
        const particlePos = new THREE.Vector3(px, py, pz)
          .applyQuaternion(rotation)
          .add(position);

        const particle = new CANNON.Body({
          mass: this.fixedCorners && (
            (x === 0 && y === 0) ||
            (x === segmentsX && y === 0) ||
            (x === 0 && y === segmentsY) ||
            (x === segmentsX && y === segmentsY)
          ) ? 0 : particleMass,
          position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
          shape: new CANNON.Particle(),
          material: this.material || undefined,
          linearDamping: this.damping
        });

        this.particles.push(particle);
        this.world?.addBody(particle);
      }
    }

    // 创建约束
    const particlesPerRow = segmentsX + 1;

    // 水平约束
    for (let y = 0; y <= segmentsY; y++) {
      for (let x = 0; x < segmentsX; x++) {
        const i1 = y * particlesPerRow + x;
        const i2 = y * particlesPerRow + x + 1;

        const p1 = this.particles[i1].position;
        const p2 = this.particles[i2].position;

        const restLength = p1.distanceTo(p2);

        this.constraints.push({
          particleA: i1,
          particleB: i2,
          restLength,
          stiffness: this.stiffness
        });
      }
    }

    // 垂直约束
    for (let x = 0; x <= segmentsX; x++) {
      for (let y = 0; y < segmentsY; y++) {
        const i1 = y * particlesPerRow + x;
        const i2 = (y + 1) * particlesPerRow + x;

        const p1 = this.particles[i1].position;
        const p2 = this.particles[i2].position;

        const restLength = p1.distanceTo(p2);

        this.constraints.push({
          particleA: i1,
          particleB: i2,
          restLength,
          stiffness: this.stiffness
        });
      }
    }

    // 对角线约束（增加稳定性）
    for (let y = 0; y < segmentsY; y++) {
      for (let x = 0; x < segmentsX; x++) {
        const i1 = y * particlesPerRow + x;
        const i2 = (y + 1) * particlesPerRow + x + 1;

        const p1 = this.particles[i1].position;
        const p2 = this.particles[i2].position;

        const restLength = p1.distanceTo(p2);

        this.constraints.push({
          particleA: i1,
          particleB: i2,
          restLength,
          stiffness: this.stiffness * 0.5 // 对角线约束刚度较低
        });

        const i3 = y * particlesPerRow + x + 1;
        const i4 = (y + 1) * particlesPerRow + x;

        const p3 = this.particles[i3].position;
        const p4 = this.particles[i4].position;

        const restLength2 = p3.distanceTo(p4);

        this.constraints.push({
          particleA: i3,
          particleB: i4,
          restLength: restLength2,
          stiffness: this.stiffness * 0.5
        });
      }
    }
  }

  /**
   * 创建绳索
   */
  private createRope(): void {
    // 从参数中获取绳索参数
    const segments = this.params.segments || 10;
    const length = this.params.length || 10;

    // 如果没有提供网格，创建一个绳索网格
    if (!this.mesh) {
      const points = [];
      for (let i = 0; i <= segments; i++) {
        points.push(new THREE.Vector3(0, -i * (length / segments), 0));
      }

      const geometry = new THREE.BufferGeometry().setFromPoints(points);
      const material = new THREE.LineBasicMaterial({ color: 0x3366cc });
      this.mesh = new THREE.Line(geometry, material);
      // TODO: 需要创建正确的 MeshComponent 实例
      // this.entity?.addComponent(new MeshComponent({ mesh: this.mesh }));
    }

    // 获取实体的变换
    const transform = this.entity?.getTransform();
    const position = transform ? transform.getPosition() : new THREE.Vector3();
    const rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();

    // 创建粒子
    const particleMass = this.mass / segments;
    const segmentLength = length / segments;

    for (let i = 0; i <= segments; i++) {
      // 计算粒子位置
      const particlePos = new THREE.Vector3(0, -i * segmentLength, 0)
        .applyQuaternion(rotation)
        .add(position);

      // 创建粒子
      const particle = new CANNON.Body({
        mass: (this.fixedEnds && (i === 0 || i === segments)) ? 0 : particleMass,
        position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
        shape: new CANNON.Particle(),
        material: this.material || undefined,
        linearDamping: this.damping
      });

      this.particles.push(particle);
      this.world?.addBody(particle);
    }

    // 创建约束
    for (let i = 0; i < segments; i++) {
      const p1 = this.particles[i].position;
      const p2 = this.particles[i + 1].position;

      const restLength = p1.distanceTo(p2);

      this.constraints.push({
        particleA: i,
        particleB: i + 1,
        restLength,
        stiffness: this.stiffness
      });
    }
  }

  /**
   * 创建体积软体
   */
  private createVolumeSoftBody(): void {
    // 从参数中获取体积软体参数
    const size = this.params.size || 1;
    const segments = this.params.segments || 3;

    // 如果没有提供网格，创建一个立方体网格
    if (!this.mesh) {
      const geometry = new THREE.BoxGeometry(size, size, size, segments, segments, segments);
      const material = new THREE.MeshStandardMaterial({
        color: 0x3366cc,
        wireframe: true
      });
      this.mesh = new THREE.Mesh(geometry, material);
      // TODO: 需要创建正确的 MeshComponent 实例
      // this.entity?.addComponent(new MeshComponent({ mesh: this.mesh }));
    }

    // 获取实体的变换
    const transform = this.entity?.getTransform();
    const position = transform ? transform.getPosition() : new THREE.Vector3();
    const rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();

    // 创建粒子
    const particleMass = this.mass / Math.pow(segments + 1, 3);
    const step = size / segments;

    // 创建3D网格的粒子
    for (let z = 0; z <= segments; z++) {
      for (let y = 0; y <= segments; y++) {
        for (let x = 0; x <= segments; x++) {
          // 计算粒子位置
          const px = (x / segments - 0.5) * size;
          const py = (y / segments - 0.5) * size;
          const pz = (z / segments - 0.5) * size;

          const particlePos = new THREE.Vector3(px, py, pz)
            .applyQuaternion(rotation)
            .add(position);

          // 创建粒子
          const particle = new CANNON.Body({
            mass: particleMass,
            position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
            shape: new CANNON.Particle(),
            material: this.material || undefined,
            linearDamping: this.damping
          });

          this.particles.push(particle);
          this.world?.addBody(particle);
        }
      }
    }

    // 创建约束
    const particlesPerRow = segments + 1;
    const particlesPerSlice = particlesPerRow * particlesPerRow;

    // X方向约束
    for (let z = 0; z <= segments; z++) {
      for (let y = 0; y <= segments; y++) {
        for (let x = 0; x < segments; x++) {
          const i1 = z * particlesPerSlice + y * particlesPerRow + x;
          const i2 = z * particlesPerSlice + y * particlesPerRow + x + 1;

          const p1 = this.particles[i1].position;
          const p2 = this.particles[i2].position;

          const restLength = p1.distanceTo(p2);

          this.constraints.push({
            particleA: i1,
            particleB: i2,
            restLength,
            stiffness: this.stiffness
          });
        }
      }
    }

    // Y方向约束
    for (let z = 0; z <= segments; z++) {
      for (let x = 0; x <= segments; x++) {
        for (let y = 0; y < segments; y++) {
          const i1 = z * particlesPerSlice + y * particlesPerRow + x;
          const i2 = z * particlesPerSlice + (y + 1) * particlesPerRow + x;

          const p1 = this.particles[i1].position;
          const p2 = this.particles[i2].position;

          const restLength = p1.distanceTo(p2);

          this.constraints.push({
            particleA: i1,
            particleB: i2,
            restLength,
            stiffness: this.stiffness
          });
        }
      }
    }

    // Z方向约束
    for (let y = 0; y <= segments; y++) {
      for (let x = 0; x <= segments; x++) {
        for (let z = 0; z < segments; z++) {
          const i1 = z * particlesPerSlice + y * particlesPerRow + x;
          const i2 = (z + 1) * particlesPerSlice + y * particlesPerRow + x;

          const p1 = this.particles[i1].position;
          const p2 = this.particles[i2].position;

          const restLength = p1.distanceTo(p2);

          this.constraints.push({
            particleA: i1,
            particleB: i2,
            restLength,
            stiffness: this.stiffness
          });
        }
      }
    }

    // 对角线约束（可选，增加稳定性）
    if (this.params.addDiagonalConstraints) {
      // 实现对角线约束...
    }
  }

  /**
   * 创建物理约束
   */
  private createPhysicsConstraints(): void {
    // 为每个约束创建物理约束
    for (const constraint of this.constraints) {
      const particleA = this.particles[constraint.particleA];
      const particleB = this.particles[constraint.particleB];

      // 创建距离约束
      const distanceConstraint = new CANNON.DistanceConstraint(
        particleA,
        particleB,
        constraint.restLength,
        constraint.stiffness
      );

      this.physicsConstraints.push(distanceConstraint);
      this.world?.addConstraint(distanceConstraint);
    }
  }

  /**
   * 求解约束
   * @param deltaTime 时间步长
   */
  public solveConstraints(deltaTime: number): void {
    if (!this.initialized) return;

    // 手动求解约束（可选，如果物理引擎的约束求解不够稳定）
    for (let i = 0; i < this.constraints.length; i++) {
      const constraint = this.constraints[i];
      const particleA = this.particles[constraint.particleA];
      const particleB = this.particles[constraint.particleB];

      // 计算当前长度
      const delta = new CANNON.Vec3();
      delta.copy(particleB.position);
      delta.vsub(particleA.position);
      const currentLength = delta.length();

      // 如果长度接近约束长度，跳过
      if (Math.abs(currentLength - constraint.restLength) < 0.001) continue;

      // 计算校正因子
      const correctionFactor = (currentLength - constraint.restLength) / currentLength;

      // 应用校正
      const correction = new CANNON.Vec3();
      correction.copy(delta);
      correction.scale(correctionFactor * 0.5 * constraint.stiffness * deltaTime);

      // 如果粒子不是固定的，应用校正
      if (particleA.mass > 0) {
        particleA.position.vadd(correction);
      }

      if (particleB.mass > 0) {
        particleB.position.vsub(correction);
      }
    }
  }

  /**
   * 更新网格
   */
  public updateMesh(): void {
    if (!this.initialized || !this.mesh) return;

    // 根据软体类型更新网格
    switch (this.softBodyType) {
      case SoftBodyType.CLOTH:
        this.updateClothMesh();
        break;
      case SoftBodyType.ROPE:
        this.updateRopeMesh();
        break;
      case SoftBodyType.VOLUME:
        this.updateVolumeMesh();
        break;
      case SoftBodyType.BALLOON:
        this.updateBalloonMesh();
        break;
      case SoftBodyType.JELLY:
        this.updateJellyMesh();
        break;
    }
  }

  /**
   * 更新布料网格
   */
  private updateClothMesh(): void {
    if (!this.mesh || !(this.mesh instanceof THREE.Mesh)) return;

    // 获取网格的顶点位置
    const geometry = this.mesh.geometry;
    const positionAttribute = geometry.getAttribute('position');

    // 确保粒子数量与顶点数量匹配
    const vertexCount = positionAttribute.count;
    const particleCount = Math.min(this.particles.length, vertexCount);

    // 更新顶点位置
    for (let i = 0; i < particleCount; i++) {
      const particle = this.particles[i];
      positionAttribute.setXYZ(
        i,
        particle.position.x,
        particle.position.y,
        particle.position.z
      );
    }

    // 标记位置属性需要更新
    positionAttribute.needsUpdate = true;

    // 更新法线
    geometry.computeVertexNormals();
  }

  /**
   * 更新绳索网格
   */
  private updateRopeMesh(): void {
    if (!this.mesh || !(this.mesh instanceof THREE.Line)) return;

    // 获取网格的顶点位置
    const geometry = this.mesh.geometry;
    const positionAttribute = geometry.getAttribute('position');

    // 确保粒子数量与顶点数量匹配
    const vertexCount = positionAttribute.count;
    const particleCount = Math.min(this.particles.length, vertexCount);

    // 更新顶点位置
    for (let i = 0; i < particleCount; i++) {
      const particle = this.particles[i];
      positionAttribute.setXYZ(
        i,
        particle.position.x,
        particle.position.y,
        particle.position.z
      );
    }

    // 标记位置属性需要更新
    positionAttribute.needsUpdate = true;
  }

  /**
   * 更新体积软体网格
   */
  private updateVolumeMesh(): void {
    if (!this.mesh || !(this.mesh instanceof THREE.Mesh)) return;

    // 获取网格的顶点位置
    const geometry = this.mesh.geometry;
    const positionAttribute = geometry.getAttribute('position');

    // 确保粒子数量与顶点数量匹配
    const vertexCount = positionAttribute.count;
    const particleCount = Math.min(this.particles.length, vertexCount);

    // 更新顶点位置
    for (let i = 0; i < particleCount; i++) {
      const particle = this.particles[i];
      positionAttribute.setXYZ(
        i,
        particle.position.x,
        particle.position.y,
        particle.position.z
      );
    }

    // 标记位置属性需要更新
    positionAttribute.needsUpdate = true;

    // 更新法线
    geometry.computeVertexNormals();
  }

  /**
   * 创建气球
   */
  private createBalloon(): void {
    // 从参数中获取气球参数
    const radius = this.params.radius || 1;
    const segments = this.params.segments || 16;
    const pressure = this.params.pressure || 10;

    // 如果没有提供网格，创建一个球体网格
    if (!this.mesh) {
      const geometry = new THREE.SphereGeometry(radius, segments, segments);
      const material = new THREE.MeshStandardMaterial({
        color: 0xff0000,
        side: THREE.DoubleSide
      });
      this.mesh = new THREE.Mesh(geometry, material);
      // TODO: 需要创建正确的 MeshComponent 实例
      // this.entity?.addComponent(new MeshComponent({ mesh: this.mesh }));
    }

    // 获取实体的变换
    const transform = this.entity?.getTransform();
    const position = transform ? transform.getPosition() : new THREE.Vector3();
    const rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();

    // 创建粒子
    const particleMass = this.mass / (segments * segments);

    // 创建球面上的粒子
    for (let i = 0; i <= segments; i++) {
      const phi = Math.PI * i / segments;
      const sinPhi = Math.sin(phi);
      const cosPhi = Math.cos(phi);

      for (let j = 0; j <= segments; j++) {
        const theta = 2 * Math.PI * j / segments;
        const sinTheta = Math.sin(theta);
        const cosTheta = Math.cos(theta);

        // 计算球面上的点
        const x = radius * sinPhi * cosTheta;
        const y = radius * cosPhi;
        const z = radius * sinPhi * sinTheta;

        // 创建粒子
        const particlePos = new THREE.Vector3(x, y, z)
          .applyQuaternion(rotation)
          .add(position);

        const particle = new CANNON.Body({
          mass: particleMass,
          position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
          shape: new CANNON.Particle(),
          material: this.material || undefined,
          linearDamping: this.damping
        });

        this.particles.push(particle);
        this.world?.addBody(particle);
      }
    }

    // 创建约束
    const particlesPerRow = segments + 1;

    // 创建表面约束
    for (let i = 0; i < segments; i++) {
      for (let j = 0; j < segments; j++) {
        const i1 = i * particlesPerRow + j;
        const i2 = i * particlesPerRow + (j + 1);
        const i3 = (i + 1) * particlesPerRow + j;
        const i4 = (i + 1) * particlesPerRow + (j + 1);

        // 水平约束
        this.addConstraint(i1, i2);

        // 垂直约束
        this.addConstraint(i1, i3);

        // 对角线约束
        this.addConstraint(i1, i4);
        this.addConstraint(i2, i3);
      }
    }

    // 存储气球参数
    this.params.pressure = pressure;
  }

  /**
   * 创建果冻
   */
  private createJelly(): void {
    // 从参数中获取果冻参数
    const size = this.params.size || 1;
    const segments = this.params.segments || 5;
    const stiffness = this.params.stiffness || 300;
    const damping = this.params.damping || 0.5;

    // 如果没有提供网格，创建一个立方体网格
    if (!this.mesh) {
      const geometry = new THREE.BoxGeometry(size, size, size, segments, segments, segments);
      const material = new THREE.MeshStandardMaterial({
        color: 0x00ff00,
        transparent: true,
        opacity: 0.8
      });
      this.mesh = new THREE.Mesh(geometry, material);
      // TODO: 需要创建正确的 MeshComponent 实例
      // this.entity?.addComponent(new MeshComponent({ mesh: this.mesh }));
    }

    // 获取实体的变换
    const transform = this.entity?.getTransform();
    const position = transform ? transform.getPosition() : new THREE.Vector3();
    const rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();

    // 创建粒子
    const particleMass = this.mass / Math.pow(segments + 1, 3);
    // const step = size / segments; // 暂时不需要，保留以备将来使用

    // 创建3D网格的粒子
    for (let z = 0; z <= segments; z++) {
      for (let y = 0; y <= segments; y++) {
        for (let x = 0; x <= segments; x++) {
          // 计算粒子位置
          const px = (x / segments - 0.5) * size;
          const py = (y / segments - 0.5) * size;
          const pz = (z / segments - 0.5) * size;

          const particlePos = new THREE.Vector3(px, py, pz)
            .applyQuaternion(rotation)
            .add(position);

          // 创建粒子
          const particle = new CANNON.Body({
            mass: particleMass,
            position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
            shape: new CANNON.Particle(),
            material: this.material || undefined,
            linearDamping: damping
          });

          this.particles.push(particle);
          this.world?.addBody(particle);
        }
      }
    }

    // 创建约束
    const particlesPerRow = segments + 1;
    const particlesPerSlice = particlesPerRow * particlesPerRow;

    // 创建内部约束
    for (let z = 0; z <= segments; z++) {
      for (let y = 0; y <= segments; y++) {
        for (let x = 0; x <= segments; x++) {
          const index = z * particlesPerSlice + y * particlesPerRow + x;

          // 连接到相邻粒子
          if (x < segments) this.addConstraint(index, index + 1, stiffness);
          if (y < segments) this.addConstraint(index, index + particlesPerRow, stiffness);
          if (z < segments) this.addConstraint(index, index + particlesPerSlice, stiffness);

          // 对角线约束（增加稳定性）
          if (x < segments && y < segments) {
            this.addConstraint(index, index + particlesPerRow + 1, stiffness * 0.7);
            this.addConstraint(index + 1, index + particlesPerRow, stiffness * 0.7);
          }

          if (y < segments && z < segments) {
            this.addConstraint(index, index + particlesPerSlice + particlesPerRow, stiffness * 0.7);
            this.addConstraint(index + particlesPerRow, index + particlesPerSlice, stiffness * 0.7);
          }

          if (x < segments && z < segments) {
            this.addConstraint(index, index + particlesPerSlice + 1, stiffness * 0.7);
            this.addConstraint(index + 1, index + particlesPerSlice, stiffness * 0.7);
          }
        }
      }
    }
  }

  /**
   * 添加约束
   * @param particleA 粒子A索引
   * @param particleB 粒子B索引
   * @param stiffness 刚度（可选）
   */
  private addConstraint(particleA: number, particleB: number, stiffness?: number): void {
    const p1 = this.particles[particleA].position;
    const p2 = this.particles[particleB].position;

    const restLength = p1.distanceTo(p2);

    this.constraints.push({
      particleA,
      particleB,
      restLength,
      stiffness: stiffness || this.stiffness
    });
  }

  /**
   * 更新气球网格
   */
  private updateBalloonMesh(): void {
    if (!this.mesh || !(this.mesh instanceof THREE.Mesh)) return;

    // 获取网格的顶点位置
    const geometry = this.mesh.geometry;
    const positionAttribute = geometry.getAttribute('position');

    // 确保粒子数量与顶点数量匹配
    const vertexCount = positionAttribute.count;
    const particleCount = Math.min(this.particles.length, vertexCount);

    // 更新顶点位置
    for (let i = 0; i < particleCount; i++) {
      const particle = this.particles[i];
      positionAttribute.setXYZ(
        i,
        particle.position.x,
        particle.position.y,
        particle.position.z
      );
    }

    // 标记位置属性需要更新
    positionAttribute.needsUpdate = true;

    // 更新法线
    geometry.computeVertexNormals();

    // 应用内部压力
    this.applyBalloonPressure();
  }

  /**
   * 应用气球内部压力
   */
  private applyBalloonPressure(): void {
    if (!this.params.pressure) return;

    // 计算气球中心
    const center = new CANNON.Vec3();
    for (const particle of this.particles) {
      center.vadd(particle.position, center);
    }
    center.scale(1 / this.particles.length, center);

    // 对每个粒子应用向外的力
    for (const particle of this.particles) {
      // 计算从中心到粒子的方向
      const direction = new CANNON.Vec3();
      direction.copy(particle.position);
      direction.vsub(center, direction);

      // 归一化方向
      const length = direction.length();
      if (length < 0.0001) continue;

      direction.scale(1 / length, direction);

      // 应用力
      const force = new CANNON.Vec3();
      force.copy(direction);
      force.scale(this.params.pressure, force);

      particle.applyForce(force, particle.position);
    }
  }

  /**
   * 更新果冻网格
   */
  private updateJellyMesh(): void {
    if (!this.mesh || !(this.mesh instanceof THREE.Mesh)) return;

    // 获取网格的顶点位置
    const geometry = this.mesh.geometry;
    const positionAttribute = geometry.getAttribute('position');

    // 确保粒子数量与顶点数量匹配
    const vertexCount = positionAttribute.count;
    const particleCount = Math.min(this.particles.length, vertexCount);

    // 更新顶点位置
    for (let i = 0; i < particleCount; i++) {
      const particle = this.particles[i];
      positionAttribute.setXYZ(
        i,
        particle.position.x,
        particle.position.y,
        particle.position.z
      );
    }

    // 标记位置属性需要更新
    positionAttribute.needsUpdate = true;

    // 更新法线
    geometry.computeVertexNormals();
  }

  /**
   * 创建调试网格
   * @returns 调试网格
   */
  public createDebugMesh(): THREE.Object3D | null {
    if (!this.initialized) return null;

    const debugObject = new THREE.Object3D();

    // 创建粒子可视化
    const particleGeometry = new THREE.SphereGeometry(0.05, 8, 8);
    const particleMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    for (const particle of this.particles) {
      const particleMesh = new THREE.Mesh(particleGeometry, particleMaterial);
      particleMesh.position.set(
        particle.position.x,
        particle.position.y,
        particle.position.z
      );
      debugObject.add(particleMesh);
    }

    // 创建约束可视化
    const constraintMaterial = new THREE.LineBasicMaterial({ color: 0x00ff00 });

    for (const constraint of this.constraints) {
      const particleA = this.particles[constraint.particleA];
      const particleB = this.particles[constraint.particleB];

      const points = [
        new THREE.Vector3(
          particleA.position.x,
          particleA.position.y,
          particleA.position.z
        ),
        new THREE.Vector3(
          particleB.position.x,
          particleB.position.y,
          particleB.position.z
        )
      ];

      const geometry = new THREE.BufferGeometry().setFromPoints(points);
      const line = new THREE.Line(geometry, constraintMaterial);
      debugObject.add(line);
    }

    return debugObject;
  }

  /**
   * 是否已初始化
   * @returns 是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new SoftBodyComponent({
      type: this.softBodyType,
      mass: this.mass,
      stiffness: this.stiffness,
      damping: this.damping,
      fixedCorners: this.fixedCorners,
      fixedEnds: this.fixedEnds,
      mesh: this.mesh,
      materialName: this.material ? 'default' : undefined,
      params: { ...this.params }
    });
  }

  /**
   * 销毁软体
   */
  public destroy(): void {
    if (!this.initialized || !this.world) return;

    // 移除所有约束
    for (const constraint of this.physicsConstraints) {
      this.world.removeConstraint(constraint);
    }

    // 移除所有粒子
    for (const particle of this.particles) {
      this.world.removeBody(particle);
    }

    this.physicsConstraints = [];
    this.particles = [];
    this.constraints = [];
    this.initialized = false;
  }
}
