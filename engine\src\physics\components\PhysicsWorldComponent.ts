/**
 * 物理世界组件
 * 为场景提供物理世界
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { Component } from '../../core/Component';

/**
 * 物理世界选项
 */
export interface PhysicsWorldOptions {
  /** 重力 */
  gravity?: THREE.Vector3;
  /** 是否允许休眠 */
  allowSleep?: boolean;
  /** 迭代次数 */
  iterations?: number;
  /** 宽相检测算法 */
  broadphase?: 'naive' | 'sap' | 'grid';
  /** 网格宽相检测的单元格大小 */
  gridBroadphaseSize?: number;
  /** 默认接触材质 */
  defaultContactMaterial?: CANNON.ContactMaterial;
  /** 默认摩擦力 */
  defaultFriction?: number;
  /** 默认恢复系数 */
  defaultRestitution?: number;
}

/**
 * 物理世界组件
 */
export class PhysicsWorldComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'PhysicsWorldComponent';

  /** 物理世界 */
  private world: CANNON.World;

  /** 重力 */
  private gravity: THREE.Vector3;

  /** 是否允许休眠 */
  private allowSleep: boolean;

  /** 迭代次数 */
  private iterations: number;

  /** 宽相检测算法 */
  private broadphase: string;

  /** 网格宽相检测的单元格大小 */
  private gridBroadphaseSize: number;

  /** 默认接触材质 */
  private defaultContactMaterial: CANNON.ContactMaterial | null;

  /** 默认摩擦力 */
  private defaultFriction: number;

  /** 默认恢复系数 */
  private defaultRestitution: number;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /**
   * 创建物理世界组件
   * @param options 物理世界选项
   */
  constructor(options: PhysicsWorldOptions = {}) {
    super(PhysicsWorldComponent.type);

    this.gravity = options.gravity ? options.gravity.clone() : new THREE.Vector3(0, -9.82, 0);
    this.allowSleep = options.allowSleep !== undefined ? options.allowSleep : true;
    this.iterations = options.iterations || 10;
    this.broadphase = options.broadphase || 'naive';
    this.gridBroadphaseSize = options.gridBroadphaseSize || 5;
    this.defaultContactMaterial = options.defaultContactMaterial || null;
    this.defaultFriction = options.defaultFriction || 0.3;
    this.defaultRestitution = options.defaultRestitution || 0.3;

    // 创建物理世界
    this.world = new CANNON.World();

    // 设置物理世界属性
    this.world.gravity.set(this.gravity.x, this.gravity.y, this.gravity.z);
    this.world.allowSleep = this.allowSleep;

    // 设置求解器迭代次数（使用类型断言）
    (this.world.solver as any).iterations = this.iterations;

    // 设置宽相检测算法
    this.setBroadphase(this.broadphase);

    // 设置默认接触材质
    if (this.defaultContactMaterial) {
      this.world.defaultContactMaterial = this.defaultContactMaterial;
    } else {
      // 创建默认材质
      const defaultMaterial = new CANNON.Material('default');
      const defaultContactMaterial = new CANNON.ContactMaterial(
        defaultMaterial,
        defaultMaterial,
        {
          friction: this.defaultFriction,
          restitution: this.defaultRestitution
        }
      );
      this.world.defaultContactMaterial = defaultContactMaterial;
    }
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    if (this.initialized || this.destroyed) return;

    this.initialized = true;
  }

  /**
   * 设置宽相检测算法
   * @param broadphase 宽相检测算法
   */
  private setBroadphase(broadphase: string): void {
    switch (broadphase) {
      case 'naive':
        this.world.broadphase = new CANNON.NaiveBroadphase();
        break;

      case 'sap':
        this.world.broadphase = new CANNON.SAPBroadphase(this.world);
        break;

      case 'grid':
        this.world.broadphase = new CANNON.GridBroadphase(
          new CANNON.Vec3(-this.gridBroadphaseSize, -this.gridBroadphaseSize, -this.gridBroadphaseSize),
          new CANNON.Vec3(this.gridBroadphaseSize, this.gridBroadphaseSize, this.gridBroadphaseSize),
          10,
          10,
          10
        );
        break;

      default:
        this.world.broadphase = new CANNON.NaiveBroadphase();
        break;
    }
  }

  /**
   * 更新物理世界
   * @param deltaTime 帧间隔时间（秒）
   * @param maxSubSteps 最大子步长
   */
  public update(deltaTime: number, maxSubSteps: number = 10): void {
    if (!this.initialized || this.destroyed) return;

    // 更新物理世界
    this.world.step(deltaTime, deltaTime, maxSubSteps);
  }

  /**
   * 获取物理世界
   * @returns 物理世界
   */
  public getWorld(): CANNON.World {
    return this.world;
  }

  /**
   * 设置重力
   * @param gravity 重力向量
   */
  public setGravity(gravity: THREE.Vector3): void {
    this.gravity = gravity.clone();
    this.world.gravity.set(gravity.x, gravity.y, gravity.z);
  }

  /**
   * 获取重力
   * @returns 重力向量
   */
  public getGravity(): THREE.Vector3 {
    return this.gravity.clone();
  }

  /**
   * 设置是否允许休眠
   * @param allowSleep 是否允许休眠
   */
  public setAllowSleep(allowSleep: boolean): void {
    this.allowSleep = allowSleep;
    this.world.allowSleep = allowSleep;
  }

  /**
   * 获取是否允许休眠
   * @returns 是否允许休眠
   */
  public getAllowSleep(): boolean {
    return this.allowSleep;
  }

  /**
   * 设置迭代次数
   * @param iterations 迭代次数
   */
  public setIterations(iterations: number): void {
    this.iterations = iterations;
    // 设置求解器迭代次数（使用类型断言）
    (this.world.solver as any).iterations = iterations;
  }

  /**
   * 获取迭代次数
   * @returns 迭代次数
   */
  public getIterations(): number {
    return this.iterations;
  }

  /**
   * 设置宽相检测算法
   * @param broadphase 宽相检测算法
   * @param gridSize 网格宽相检测的单元格大小（仅适用于grid算法）
   */
  public setBroadphaseAlgorithm(broadphase: 'naive' | 'sap' | 'grid', gridSize?: number): void {
    this.broadphase = broadphase;

    if (gridSize !== undefined) {
      this.gridBroadphaseSize = gridSize;
    }

    this.setBroadphase(broadphase);
  }

  /**
   * 获取宽相检测算法
   * @returns 宽相检测算法
   */
  public getBroadphaseAlgorithm(): string {
    return this.broadphase;
  }

  /**
   * 创建材质
   * @param name 材质名称
   * @returns 材质
   */
  public createMaterial(name: string): CANNON.Material {
    return new CANNON.Material(name);
  }

  /**
   * 创建接触材质
   * @param materialA 材质A
   * @param materialB 材质B
   * @param options 接触材质选项
   * @returns 接触材质
   */
  public createContactMaterial(
    materialA: CANNON.Material,
    materialB: CANNON.Material,
    options: {
      friction?: number;
      restitution?: number;
      contactEquationStiffness?: number;
      contactEquationRelaxation?: number;
      frictionEquationStiffness?: number;
      frictionEquationRelaxation?: number;
    } = {}
  ): CANNON.ContactMaterial {
    const contactMaterial = new CANNON.ContactMaterial(materialA, materialB, options);
    this.world.addContactMaterial(contactMaterial);
    return contactMaterial;
  }

  /**
   * 射线检测
   * @param from 起点
   * @param to 终点
   * @param options 射线检测选项
   * @returns 射线检测结果
   */
  public raycast(
    from: THREE.Vector3,
    to: THREE.Vector3,
    options: {
      skipBackfaces?: boolean;
      collisionFilterMask?: number;
      collisionFilterGroup?: number;
    } = {}
  ): CANNON.RaycastResult | null {
    const rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
    const rayTo = new CANNON.Vec3(to.x, to.y, to.z);

    const result = new CANNON.RaycastResult();
    this.world.raycastClosest(rayFrom, rayTo, options, result);

    return result.hasHit ? result : null;
  }

  /**
   * 射线检测（多个结果）
   * @param from 起点
   * @param to 终点
   * @param options 射线检测选项
   * @returns 射线检测结果数组
   */
  public raycastAll(
    from: THREE.Vector3,
    to: THREE.Vector3,
    options: {
      skipBackfaces?: boolean;
      collisionFilterMask?: number;
      collisionFilterGroup?: number;
    } = {}
  ): CANNON.RaycastResult[] {
    const rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
    const rayTo = new CANNON.Vec3(to.x, to.y, to.z);

    const results: CANNON.RaycastResult[] = [];
    this.world.raycastAll(rayFrom, rayTo, options, (result: CANNON.RaycastResult) => {
      results.push(result);
    });

    return results;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (event: any) => void): void {
    this.world.addEventListener(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (event: any) => void): void {
    this.world.removeEventListener(event, callback);
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new PhysicsWorldComponent({
      gravity: this.gravity.clone(),
      allowSleep: this.allowSleep,
      iterations: this.iterations,
      broadphase: this.broadphase as 'naive' | 'sap' | 'grid',
      gridBroadphaseSize: this.gridBroadphaseSize,
      defaultContactMaterial: this.defaultContactMaterial,
      defaultFriction: this.defaultFriction,
      defaultRestitution: this.defaultRestitution
    });
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    if (this.destroyed) return;

    // 清空物理世界
    while (this.world.bodies.length > 0) {
      this.world.removeBody(this.world.bodies[0]);
    }

    while (this.world.constraints.length > 0) {
      this.world.removeConstraint(this.world.constraints[0]);
    }

    this.initialized = false;
    this.destroyed = true;

    // 调用基类的dispose方法
    super.dispose();
  }
}
