/**
 * 铰链约束
 * 将两个物体通过一个轴连接起来，允许它们绕该轴旋转
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

/**
 * 铰链约束选项
 */
export interface HingeConstraintOptions {
  /** 源物体上的连接点（局部坐标） */
  pivotA?: THREE.Vector3;
  /** 目标物体上的连接点（局部坐标） */
  pivotB?: THREE.Vector3;
  /** 源物体上的轴（局部坐标） */
  axisA?: THREE.Vector3;
  /** 目标物体上的轴（局部坐标） */
  axisB?: THREE.Vector3;
  /** 最大力 */
  maxForce?: number;
  /** 是否允许连接的物体之间碰撞 */
  collideConnected?: boolean;
  /** 电机启用 */
  motorEnabled?: boolean;
  /** 电机速度 */
  motorSpeed?: number;
  /** 电机最大力 */
  motorMaxForce?: number;
  /** 角度限制启用 */
  angleEnabled?: boolean;
  /** 最小角度 */
  angleMin?: number;
  /** 最大角度 */
  angleMax?: number;
}

/**
 * 铰链约束
 */
export class HingeConstraint extends PhysicsConstraint {
  /** 组件类型 */
  public static readonly type: string = 'HingeConstraint';

  /** 源物体上的连接点（局部坐标） */
  private pivotA: CANNON.Vec3;

  /** 目标物体上的连接点（局部坐标） */
  private pivotB: CANNON.Vec3;

  /** 源物体上的轴（局部坐标） */
  private axisA: CANNON.Vec3;

  /** 目标物体上的轴（局部坐标） */
  private axisB: CANNON.Vec3;

  /** 最大力 */
  private maxForce: number;

  /** 电机启用 */
  private motorEnabled: boolean;

  /** 电机速度 */
  private motorSpeed: number;

  /** 电机最大力 */
  private motorMaxForce: number;

  /** 角度限制启用 */
  private angleEnabled: boolean;

  /** 最小角度 */
  private angleMin: number;

  /** 最大角度 */
  private angleMax: number;

  /**
   * 创建铰链约束
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(targetEntity: Entity | null = null, options: HingeConstraintOptions = {}) {
    super(ConstraintType.HINGE, targetEntity, options);

    // 设置源物体上的连接点
    const pivotA = options.pivotA || new THREE.Vector3(0, 0, 0);
    this.pivotA = new CANNON.Vec3(pivotA.x, pivotA.y, pivotA.z);

    // 设置目标物体上的连接点
    const pivotB = options.pivotB || new THREE.Vector3(0, 0, 0);
    this.pivotB = new CANNON.Vec3(pivotB.x, pivotB.y, pivotB.z);

    // 设置源物体上的轴
    const axisA = options.axisA || new THREE.Vector3(1, 0, 0);
    this.axisA = new CANNON.Vec3(axisA.x, axisA.y, axisA.z);
    this.axisA.normalize();

    // 设置目标物体上的轴
    const axisB = options.axisB || new THREE.Vector3(1, 0, 0);
    this.axisB = new CANNON.Vec3(axisB.x, axisB.y, axisB.z);
    this.axisB.normalize();

    // 设置最大力
    this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;

    // 设置电机参数
    this.motorEnabled = options.motorEnabled || false;
    this.motorSpeed = options.motorSpeed || 0;
    this.motorMaxForce = options.motorMaxForce || 1e6;

    // 设置角度限制参数
    this.angleEnabled = options.angleEnabled || false;
    this.angleMin = options.angleMin || 0;
    this.angleMax = options.angleMax || 0;
  }

  /**
   * 创建约束
   */
  protected createConstraint(): void {
    // 获取源物体和目标物体
    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    // 如果没有源物体或目标物体，则无法创建约束
    if (!bodyA || !bodyB) {
      console.warn('无法创建铰链约束：缺少源物体或目标物体');
      return;
    }

    // 创建铰链约束
    this.constraint = new CANNON.HingeConstraint(bodyA, bodyB, {
      pivotA: this.pivotA,
      pivotB: this.pivotB,
      axisA: this.axisA,
      axisB: this.axisB,
      maxForce: this.maxForce
    });

    // 设置碰撞连接
    this.constraint.collideConnected = this.collideConnected;

    // 设置电机
    if (this.motorEnabled) {
      this.enableMotor();
      this.setMotorSpeed(this.motorSpeed);
      this.setMotorMaxForce(this.motorMaxForce);
    }

    // 设置角度限制
    if (this.angleEnabled) {
      this.enableAngleLimits();
      this.setAngleLimits(this.angleMin, this.angleMax);
    }
  }

  /**
   * 设置源物体上的连接点
   * @param pivot 连接点（局部坐标）
   */
  public setPivotA(pivot: THREE.Vector3): void {
    this.pivotA.set(pivot.x, pivot.y, pivot.z);

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取源物体上的连接点
   * @returns 连接点（局部坐标）
   */
  public getPivotA(): THREE.Vector3 {
    return new THREE.Vector3(this.pivotA.x, this.pivotA.y, this.pivotA.z);
  }

  /**
   * 设置目标物体上的连接点
   * @param pivot 连接点（局部坐标）
   */
  public setPivotB(pivot: THREE.Vector3): void {
    this.pivotB.set(pivot.x, pivot.y, pivot.z);

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取目标物体上的连接点
   * @returns 连接点（局部坐标）
   */
  public getPivotB(): THREE.Vector3 {
    return new THREE.Vector3(this.pivotB.x, this.pivotB.y, this.pivotB.z);
  }

  /**
   * 设置源物体上的轴
   * @param axis 轴（局部坐标）
   */
  public setAxisA(axis: THREE.Vector3): void {
    this.axisA.set(axis.x, axis.y, axis.z);
    this.axisA.normalize();

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取源物体上的轴
   * @returns 轴（局部坐标）
   */
  public getAxisA(): THREE.Vector3 {
    return new THREE.Vector3(this.axisA.x, this.axisA.y, this.axisA.z);
  }

  /**
   * 设置目标物体上的轴
   * @param axis 轴（局部坐标）
   */
  public setAxisB(axis: THREE.Vector3): void {
    this.axisB.set(axis.x, axis.y, axis.z);
    this.axisB.normalize();

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取目标物体上的轴
   * @returns 轴（局部坐标）
   */
  public getAxisB(): THREE.Vector3 {
    return new THREE.Vector3(this.axisB.x, this.axisB.y, this.axisB.z);
  }

  /**
   * 启用电机
   */
  public enableMotor(): void {
    this.motorEnabled = true;

    if (this.constraint instanceof CANNON.HingeConstraint) {
      this.constraint.enableMotor();
    }
  }

  /**
   * 禁用电机
   */
  public disableMotor(): void {
    this.motorEnabled = false;

    if (this.constraint instanceof CANNON.HingeConstraint) {
      this.constraint.disableMotor();
    }
  }

  /**
   * 设置电机速度
   * @param speed 速度（弧度/秒）
   */
  public setMotorSpeed(speed: number): void {
    this.motorSpeed = speed;

    if (this.constraint instanceof CANNON.HingeConstraint) {
      this.constraint.setMotorSpeed(speed);
    }
  }

  /**
   * 获取电机速度
   * @returns 速度（弧度/秒）
   */
  public getMotorSpeed(): number {
    return this.motorSpeed;
  }

  /**
   * 设置电机最大力
   * @param maxForce 最大力
   */
  public setMotorMaxForce(maxForce: number): void {
    this.motorMaxForce = maxForce;

    if (this.constraint instanceof CANNON.HingeConstraint) {
      // 使用CANNON.js提供的setMotorMaxForce方法
      this.constraint.setMotorMaxForce(maxForce);
    }
  }

  /**
   * 获取电机最大力
   * @returns 最大力
   */
  public getMotorMaxForce(): number {
    return this.motorMaxForce;
  }

  /**
   * 启用角度限制
   * 注意：CANNON.js的HingeConstraint没有内置的角度限制功能
   * 这里只是设置内部状态，实际限制需要在物理更新中手动实现
   */
  public enableAngleLimits(): void {
    this.angleEnabled = true;
    console.warn('CANNON.js的HingeConstraint不支持内置角度限制，需要手动实现');
  }

  /**
   * 禁用角度限制
   */
  public disableAngleLimits(): void {
    this.angleEnabled = false;
  }

  /**
   * 设置角度限制
   * @param min 最小角度（弧度）
   * @param max 最大角度（弧度）
   * 注意：CANNON.js的HingeConstraint没有内置的角度限制功能
   */
  public setAngleLimits(min: number, max: number): void {
    this.angleMin = min;
    this.angleMax = max;

    if (this.angleEnabled) {
      console.warn('CANNON.js的HingeConstraint不支持内置角度限制，需要手动实现');
    }
  }

  /**
   * 获取最小角度
   * @returns 最小角度（弧度）
   */
  public getAngleMin(): number {
    return this.angleMin;
  }

  /**
   * 获取最大角度
   * @returns 最大角度（弧度）
   */
  public getAngleMax(): number {
    return this.angleMax;
  }

  /**
   * 获取当前角度
   * @returns 当前角度（弧度）
   * 注意：CANNON.js的HingeConstraint没有内置的getAngle方法
   * 需要通过计算两个物体的相对旋转来获取角度
   */
  public getAngle(): number {
    if (this.constraint instanceof CANNON.HingeConstraint) {
      // CANNON.js没有内置的getAngle方法，这里返回0
      // 实际应用中需要通过计算两个物体的相对旋转来获取角度
      console.warn('CANNON.js的HingeConstraint不支持内置getAngle方法，需要手动计算');
      return 0;
    }
    return 0;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new HingeConstraint(this.targetEntity, {
      pivotA: new THREE.Vector3(this.pivotA.x, this.pivotA.y, this.pivotA.z),
      pivotB: new THREE.Vector3(this.pivotB.x, this.pivotB.y, this.pivotB.z),
      axisA: new THREE.Vector3(this.axisA.x, this.axisA.y, this.axisA.z),
      axisB: new THREE.Vector3(this.axisB.x, this.axisB.y, this.axisB.z),
      maxForce: this.maxForce,
      collideConnected: this.collideConnected,
      motorEnabled: this.motorEnabled,
      motorSpeed: this.motorSpeed,
      motorMaxForce: this.motorMaxForce,
      angleEnabled: this.angleEnabled,
      angleMin: this.angleMin,
      angleMax: this.angleMax
    });
  }

  /**
   * 重新创建约束
   */
  private recreateConstraint(): void {
    if (this.initialized && this.constraint && this.world) {
      this.world.removeConstraint(this.constraint);
      this.constraint = null;
      this.initialized = false;
      this.initialize(this.world);
    }
  }
}
