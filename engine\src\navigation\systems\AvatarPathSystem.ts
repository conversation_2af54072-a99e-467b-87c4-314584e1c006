/**
 * 数字人路径系统
 */
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { AvatarPathComponent } from '../components/AvatarPathComponent';
import { PathFollowingComponent } from '../components/PathFollowingComponent';
import { AvatarPath } from '../path/AvatarPath';
import { PathValidator } from '../path/PathValidator';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 系统选项
 */
export interface AvatarPathSystemOptions {
  /** 是否启用调试 */
  debug?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用路径验证 */
  enableValidation?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 最大同时运行的路径数量 */
  maxConcurrentPaths?: number;
}

/**
 * 路径系统性能统计
 */
interface AvatarPathPerformanceStats {
  /** 活动路径数量 */
  activePathCount: number;
  /** 总路径数量 */
  totalPathCount: number;
  /** 平均更新时间 */
  averageUpdateTime: number;
  /** 最大更新时间 */
  maxUpdateTime: number;
  /** 内存使用量 */
  memoryUsage: number;
}

/**
 * 数字人路径系统
 */
export class AvatarPathSystem extends System {
  /** 路径系统选项 */
  private pathSystemOptions: AvatarPathSystemOptions;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 路径组件映射 */
  private pathComponents: Map<string, AvatarPathComponent> = new Map();
  /** 路径跟随组件映射 */
  private followingComponents: Map<string, PathFollowingComponent> = new Map();
  /** 全局路径存储 */
  private globalPaths: Map<string, AvatarPath> = new Map();
  /** 路径系统性能统计 */
  private pathPerformanceStats: AvatarPathPerformanceStats = {
    activePathCount: 0,
    totalPathCount: 0,
    averageUpdateTime: 0,
    maxUpdateTime: 0,
    memoryUsage: 0
  };
  /** 更新时间记录 */
  private updateTimes: number[] = [];
  /** 最大记录数量 */
  private readonly MAX_UPDATE_RECORDS = 100;

  /**
   * 构造函数
   * @param options 系统选项
   */
  constructor(options: AvatarPathSystemOptions = {}) {
    super(10); // 优先级10，在物理系统之后，渲染系统之前

    this.pathSystemOptions = {
      debug: false,
      updateFrequency: 60,
      enableValidation: true,
      enablePerformanceMonitoring: false,
      maxConcurrentPaths: 100,
      ...options
    };

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', '数字人路径系统初始化');
    }
  }

  /**
   * 系统初始化
   */
  public initialize(): void {
    super.initialize();

    // 监听实体添加和移除事件
    if (this.world) {
      this.world.on('entityAdded', this.onEntityAdded.bind(this));
      this.world.on('entityRemoved', this.onEntityRemoved.bind(this));
    }

    // 扫描现有实体
    this.scanExistingEntities();

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', '系统初始化完成');
    }
  }

  /**
   * 系统更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    const startTime = performance.now();

    // 更新所有路径组件
    this.pathComponents.forEach((component, entityId) => {
      if (component && component.isEnabled()) {
        component.update(deltaTime);
      } else if (!component) {
        this.pathComponents.delete(entityId);
      }
    });

    // 更新所有路径跟随组件
    this.followingComponents.forEach((component, entityId) => {
      if (component && component.isEnabled()) {
        component.update(deltaTime);
      } else if (!component) {
        this.followingComponents.delete(entityId);
      }
    });

    // 性能监控
    if (this.pathSystemOptions.enablePerformanceMonitoring) {
      this.updatePathPerformanceStats(performance.now() - startTime);
    }
  }

  /**
   * 添加全局路径
   * @param path 路径实例
   */
  public addGlobalPath(path: AvatarPath): void {
    // 验证路径
    if (this.pathSystemOptions.enableValidation) {
      const validation = PathValidator.validatePath(path);
      if (!validation.valid) {
        Debug.error('AvatarPathSystem', `路径验证失败: ${validation.errors.join(', ')}`);
        return;
      }
    }

    this.globalPaths.set(path.id, path);
    this.eventEmitter.emit('globalPathAdded', { path });

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', `添加全局路径: ${path.name} (${path.id})`);
    }
  }

  /**
   * 移除全局路径
   * @param pathId 路径ID
   */
  public removeGlobalPath(pathId: string): boolean {
    const path = this.globalPaths.get(pathId);
    if (!path) return false;

    this.globalPaths.delete(pathId);
    this.eventEmitter.emit('globalPathRemoved', { pathId });

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', `移除全局路径: ${path.name} (${pathId})`);
    }

    return true;
  }

  /**
   * 获取全局路径
   * @param pathId 路径ID
   */
  public getGlobalPath(pathId: string): AvatarPath | null {
    return this.globalPaths.get(pathId) || null;
  }

  /**
   * 获取所有全局路径
   */
  public getAllGlobalPaths(): AvatarPath[] {
    return Array.from(this.globalPaths.values());
  }

  /**
   * 为实体创建路径
   * @param entity 实体
   * @param pathData 路径数据
   * @returns 路径组件
   */
  public createPathForEntity(entity: Entity, pathData: any): AvatarPathComponent | null {
    // 检查实体是否已有路径组件
    let pathComponent = entity.getComponent(AvatarPathComponent.TYPE) as AvatarPathComponent;
    
    if (!pathComponent) {
      // 创建新的路径组件
      pathComponent = new AvatarPathComponent(entity, { pathData });
      entity.addComponent(pathComponent);
      
      // 注册组件
      this.pathComponents.set(entity.id, pathComponent);
      
      // 监听组件事件
      this.setupComponentEvents(pathComponent);
    } else {
      // 添加路径到现有组件
      pathComponent.addPath(pathData);
    }

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', `为实体 ${entity.id} 创建路径`);
    }

    return pathComponent;
  }

  /**
   * 为实体分配全局路径
   * @param entity 实体
   * @param pathId 全局路径ID
   * @returns 是否成功分配
   */
  public assignGlobalPathToEntity(entity: Entity, pathId: string): boolean {
    const globalPath = this.globalPaths.get(pathId);
    if (!globalPath) {
      Debug.warn('AvatarPathSystem', `全局路径 ${pathId} 不存在`);
      return false;
    }

    // 克隆路径数据
    const pathData = globalPath.toJSON();
    pathData.id = `${pathId}_${entity.id}`; // 生成唯一ID
    pathData.avatarId = entity.id;

    const pathComponent = this.createPathForEntity(entity, pathData);
    return pathComponent !== null;
  }

  /**
   * 开始实体路径
   * @param entityId 实体ID
   * @param pathId 路径ID（可选）
   */
  public startEntityPath(entityId: string, pathId?: string): boolean {
    const pathComponent = this.pathComponents.get(entityId);
    if (!pathComponent) {
      Debug.warn('AvatarPathSystem', `实体 ${entityId} 没有路径组件`);
      return false;
    }

    if (pathId) {
      pathComponent.setActivePath(pathId);
    }

    pathComponent.startPath();
    return true;
  }

  /**
   * 停止实体路径
   * @param entityId 实体ID
   */
  public stopEntityPath(entityId: string): boolean {
    const pathComponent = this.pathComponents.get(entityId);
    if (!pathComponent) return false;

    pathComponent.stopPath();
    return true;
  }

  /**
   * 暂停实体路径
   * @param entityId 实体ID
   */
  public pauseEntityPath(entityId: string): boolean {
    const pathComponent = this.pathComponents.get(entityId);
    if (!pathComponent) return false;

    pathComponent.pausePath();
    return true;
  }

  /**
   * 恢复实体路径
   * @param entityId 实体ID
   */
  public resumeEntityPath(entityId: string): boolean {
    const pathComponent = this.pathComponents.get(entityId);
    if (!pathComponent) return false;

    pathComponent.resumePath();
    return true;
  }

  /**
   * 获取实体的路径组件
   * @param entityId 实体ID
   */
  public getEntityPathComponent(entityId: string): AvatarPathComponent | null {
    return this.pathComponents.get(entityId) || null;
  }

  /**
   * 获取路径系统性能统计
   */
  public getPathPerformanceStats(): AvatarPathPerformanceStats {
    return { ...this.pathPerformanceStats };
  }

  /**
   * 获取系统性能统计（重写基类方法）
   * @returns 系统性能统计
   */
  public getSystemPerformanceStats(): import('../../core/System').SystemPerformanceStats {
    // 获取基类的性能统计
    const baseStats = super.getPerformanceStats();

    // 将路径组件数量作为处理的实体数量
    baseStats.processedEntities = this.pathComponents.size;

    return baseStats;
  }

  /**
   * 扫描现有实体
   */
  private scanExistingEntities(): void {
    if (this.world) {
      this.world.getEntities().forEach(entity => {
        this.onEntityAdded(entity);
      });
    }
  }

  /**
   * 实体添加事件处理
   * @param entity 实体
   */
  private onEntityAdded(entity: Entity): void {
    // 检查是否有路径组件
    const pathComponent = entity.getComponent(AvatarPathComponent.TYPE) as AvatarPathComponent;
    if (pathComponent) {
      this.pathComponents.set(entity.id, pathComponent);
      this.setupComponentEvents(pathComponent);
    }

    // 检查是否有路径跟随组件
    const followingComponent = entity.getComponent(PathFollowingComponent.TYPE) as PathFollowingComponent;
    if (followingComponent) {
      this.followingComponents.set(entity.id, followingComponent);
    }
  }

  /**
   * 实体移除事件处理
   * @param entity 实体
   */
  private onEntityRemoved(entity: Entity): void {
    this.pathComponents.delete(entity.id);
    this.followingComponents.delete(entity.id);
  }

  /**
   * 设置组件事件监听
   * @param component 路径组件
   */
  private setupComponentEvents(component: AvatarPathComponent): void {
    component.addEventListener('pathStarted', (data) => {
      this.eventEmitter.emit('pathStarted', data);
    });

    component.addEventListener('pathCompleted', (data) => {
      this.eventEmitter.emit('pathCompleted', data);
    });

    component.addEventListener('waypointReached', (data) => {
      this.eventEmitter.emit('waypointReached', data);
    });

    component.addEventListener('triggerActivated', (data) => {
      this.eventEmitter.emit('triggerActivated', data);
    });
  }

  /**
   * 更新路径系统性能统计
   * @param updateTime 更新时间
   */
  private updatePathPerformanceStats(updateTime: number): void {
    // 记录更新时间
    this.updateTimes.push(updateTime);
    if (this.updateTimes.length > this.MAX_UPDATE_RECORDS) {
      this.updateTimes.shift();
    }

    // 计算统计数据
    this.pathPerformanceStats.activePathCount = this.pathComponents.size;
    this.pathPerformanceStats.totalPathCount = this.globalPaths.size;
    this.pathPerformanceStats.averageUpdateTime = this.updateTimes.reduce((a, b) => a + b, 0) / this.updateTimes.length;
    this.pathPerformanceStats.maxUpdateTime = Math.max(...this.updateTimes);

    // 估算内存使用量（简化计算）
    this.pathPerformanceStats.memoryUsage = (this.pathComponents.size + this.globalPaths.size) * 1024; // 假设每个路径1KB
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 编辑全局路径
   * @param pathId 路径ID
   * @param pathData 新的路径数据
   */
  public editGlobalPath(pathId: string, pathData: any): boolean {
    const path = this.globalPaths.get(pathId);
    if (!path) {
      Debug.warn('AvatarPathSystem', `全局路径 ${pathId} 不存在`);
      return false;
    }

    // 验证新路径数据
    if (this.pathSystemOptions.enableValidation) {
      const tempPath = new AvatarPath(pathData);
      const validation = PathValidator.validatePath(tempPath);
      if (!validation.valid) {
        Debug.error('AvatarPathSystem', `路径验证失败: ${validation.errors.join(', ')}`);
        return false;
      }
    }

    // 更新路径
    path.updateFromData(pathData);
    this.eventEmitter.emit('globalPathEdited', { pathId, path });

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', `编辑全局路径: ${path.name} (${pathId})`);
    }

    return true;
  }

  /**
   * 复制全局路径
   * @param pathId 源路径ID
   * @param newName 新路径名称
   */
  public duplicateGlobalPath(pathId: string, newName?: string): AvatarPath | null {
    const sourcePath = this.globalPaths.get(pathId);
    if (!sourcePath) {
      Debug.warn('AvatarPathSystem', `源路径 ${pathId} 不存在`);
      return null;
    }

    // 创建路径副本
    const pathData = sourcePath.toJSON();
    pathData.id = `${pathId}_copy_${Date.now()}`;
    pathData.name = newName || `${sourcePath.name} (副本)`;

    const newPath = new AvatarPath(pathData);
    this.globalPaths.set(newPath.id, newPath);
    this.eventEmitter.emit('globalPathDuplicated', { originalId: pathId, newPath });

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', `复制全局路径: ${newPath.name} (${newPath.id})`);
    }

    return newPath;
  }

  /**
   * 获取路径统计信息
   * @param pathId 路径ID
   */
  public getPathStatistics(pathId: string): any {
    const path = this.globalPaths.get(pathId);
    if (!path) return null;

    let usageCount = 0;
    let activeUsageCount = 0;

    // 统计路径使用情况
    this.pathComponents.forEach(component => {
      if (component.getActivePath()?.id === pathId) {
        usageCount++;
        if (component.getPathProgress() > 0 && component.getPathProgress() < 1) {
          activeUsageCount++;
        }
      }
    });

    return {
      pathId,
      name: path.name,
      pointCount: path.points.length,
      totalDuration: path.totalDuration,
      usageCount,
      activeUsageCount,
      averageSpeed: path.getAverageSpeed(),
      totalDistance: path.getTotalDistance()
    };
  }

  /**
   * 优化全局路径
   * @param pathId 路径ID
   * @param tolerance 优化容差
   */
  public optimizeGlobalPath(pathId: string, tolerance: number = 0.1): boolean {
    const path = this.globalPaths.get(pathId);
    if (!path) {
      Debug.warn('AvatarPathSystem', `路径 ${pathId} 不存在`);
      return false;
    }

    const originalPointCount = path.points.length;
    path.optimize(tolerance);
    const optimizedPointCount = path.points.length;

    this.eventEmitter.emit('globalPathOptimized', {
      pathId,
      originalPointCount,
      optimizedPointCount,
      reduction: originalPointCount - optimizedPointCount
    });

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', `优化路径 ${path.name}: ${originalPointCount} -> ${optimizedPointCount} 点`);
    }

    return true;
  }

  /**
   * 批量优化所有全局路径
   * @param tolerance 优化容差
   */
  public optimizeAllGlobalPaths(tolerance: number = 0.1): void {
    let totalReduction = 0;
    let optimizedCount = 0;

    this.globalPaths.forEach((path, pathId) => {
      const originalCount = path.points.length;
      path.optimize(tolerance);
      const reduction = originalCount - path.points.length;

      if (reduction > 0) {
        totalReduction += reduction;
        optimizedCount++;
      }
    });

    this.eventEmitter.emit('allPathsOptimized', {
      optimizedCount,
      totalReduction,
      totalPaths: this.globalPaths.size
    });

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', `批量优化完成: ${optimizedCount}/${this.globalPaths.size} 路径被优化，总共减少 ${totalReduction} 个点`);
    }
  }

  /**
   * 获取路径可视化数据
   * @param pathId 路径ID
   */
  public getPathVisualizationData(pathId: string): any {
    const path = this.globalPaths.get(pathId);
    if (!path) return null;

    return {
      pathId,
      name: path.name,
      points: path.points.map(point => ({
        position: point.position.toArray(),
        waitTime: point.waitTime,
        speed: point.speed,
        animation: point.animation
      })),
      boundingBox: path.getBoundingBox(),
      totalDuration: path.totalDuration,
      totalDistance: path.getTotalDistance(),
      averageSpeed: path.getAverageSpeed()
    };
  }

  /**
   * 设置路径LOD（细节层次）
   * @param pathId 路径ID
   * @param lodLevel LOD级别 (0-3)
   */
  public setPathLOD(pathId: string, lodLevel: number): boolean {
    const path = this.globalPaths.get(pathId);
    if (!path) return false;

    // 根据LOD级别调整路径细节
    const tolerance = [0.01, 0.1, 0.5, 1.0][Math.max(0, Math.min(3, lodLevel))];
    path.optimize(tolerance);

    this.eventEmitter.emit('pathLODChanged', { pathId, lodLevel, tolerance });
    return true;
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 停止所有路径
    this.pathComponents.forEach(component => {
      component.stopPath();
    });

    // 清理数据
    this.pathComponents.clear();
    this.followingComponents.clear();
    this.globalPaths.clear();

    // 移除事件监听
    this.world?.off('entityAdded', this.onEntityAdded.bind(this));
    this.world?.off('entityRemoved', this.onEntityRemoved.bind(this));

    this.eventEmitter.removeAllListeners();
    super.dispose();

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', '系统已销毁');
    }
  }
}
