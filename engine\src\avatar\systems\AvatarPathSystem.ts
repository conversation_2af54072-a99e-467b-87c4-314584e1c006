/**
 * 数字人路径系统
 * 管理数字人的路径跟随功能
 */
import { System } from '../../core/System';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { AvatarPathFollowingComponent } from '../components/AvatarPathFollowingComponent';
import { EnhancedAvatarComponent } from '../components/EnhancedAvatarComponent';
import { AvatarPath } from '../../navigation/path/AvatarPath';
import { PathFollowingState } from '../../navigation/components/PathFollowingComponent';
// 使用接口避免循环引用
interface INavigationPathSystem {
  getAllGlobalPaths(): AvatarPath[];
  getGlobalPath(pathId: string): AvatarPath | null;
  addGlobalPath(path: AvatarPath): void;
}
import { EventEmitter } from '../../utils/EventEmitter';
import { Logger } from '../../utils/Logger';

/**
 * 数字人路径系统选项
 */
export interface AvatarPathSystemOptions {
  /** 是否启用调试 */
  debug?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用自动路径分配 */
  enableAutoPathAssignment?: boolean;
  /** 是否启用路径优化 */
  enablePathOptimization?: boolean;
  /** 最大同时跟随路径的数字人数量 */
  maxConcurrentFollowers?: number;
}

/**
 * 数字人路径统计
 */
interface AvatarPathStats {
  /** 总数字人数量 */
  totalAvatars: number;
  /** 正在跟随路径的数字人数量 */
  followingAvatars: number;
  /** 暂停的数字人数量 */
  pausedAvatars: number;
  /** 完成路径的数字人数量 */
  completedAvatars: number;
  /** 平均路径完成时间 */
  averageCompletionTime: number;
}

/**
 * 数字人路径系统
 */
export class AvatarPathSystem extends System {
  private logger = new Logger('AvatarPathSystem');
  private eventEmitter = new EventEmitter();
  
  // 系统选项
  private systemOptions: AvatarPathSystemOptions;
  
  // 组件管理
  private avatarPathComponents = new Map<string, AvatarPathFollowingComponent>();
  private avatarComponents = new Map<string, EnhancedAvatarComponent>();
  
  // 导航路径系统引用（使用接口避免循环引用）
  private navigationPathSystem: INavigationPathSystem | null = null;
  
  // 统计信息
  private stats: AvatarPathStats = {
    totalAvatars: 0,
    followingAvatars: 0,
    pausedAvatars: 0,
    completedAvatars: 0,
    averageCompletionTime: 0
  };
  
  // 路径完成时间记录
  private completionTimes: number[] = [];
  private readonly MAX_COMPLETION_RECORDS = 100;

  // 性能优化定时器
  private optimizationTimer: number = 0;
  private readonly OPTIMIZATION_INTERVAL = 30000; // 30秒

  // 健康检查定时器
  private healthCheckTimer: number = 0;
  private readonly HEALTH_CHECK_INTERVAL = 60000; // 60秒

  /**
   * 构造函数
   * @param options 系统选项
   */
  constructor(options: AvatarPathSystemOptions = {}) {
    super(15); // 优先级15，在导航系统之后

    this.systemOptions = {
      debug: false,
      updateFrequency: 60,
      enableAutoPathAssignment: false,
      enablePathOptimization: true,
      maxConcurrentFollowers: 50,
      ...options
    };

    this.logger.info('数字人路径系统初始化', this.systemOptions);
  }

  /**
   * 系统初始化
   */
  public initialize(): void {
    super.initialize();

    // 获取导航路径系统（注意：这里应该获取导航模块的路径系统，而不是当前系统）
    // 暂时注释掉，因为需要导入具体的系统类
    // this.navigationPathSystem = this.world?.getSystem(NavigationPathSystem);
    // if (!this.navigationPathSystem) {
    //   this.logger.warn('未找到导航路径系统，某些功能可能受限');
    // }

    // 监听世界事件
    this.world?.on('entityAdded', this.onEntityAdded.bind(this));
    this.world?.on('entityRemoved', this.onEntityRemoved.bind(this));

    // 扫描现有实体
    this.scanExistingEntities();

    this.logger.info('数字人路径系统初始化完成');
  }

  /**
   * 系统更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    // 更新所有数字人路径组件
    this.avatarPathComponents.forEach((component, entityId) => {
      if (component) {
        try {
          component.update(deltaTime);
        } catch (error) {
          this.handlePathFollowingError(entityId, error);
        }
      } else {
        this.avatarPathComponents.delete(entityId);
      }
    });

    // 更新统计信息
    this.updateStatistics();

    // 自动路径分配
    if (this.systemOptions.enableAutoPathAssignment) {
      this.performAutoPathAssignment();
    }

    // 定期性能优化
    this.optimizationTimer += deltaTime * 1000;
    if (this.optimizationTimer >= this.OPTIMIZATION_INTERVAL) {
      this.optimizePathPerformance();
      this.optimizationTimer = 0;
    }

    // 定期健康检查
    this.healthCheckTimer += deltaTime * 1000;
    if (this.healthCheckTimer >= this.HEALTH_CHECK_INTERVAL) {
      const health = this.getSystemHealth();
      if (health.status !== 'healthy') {
        this.logger.warn('系统健康检查发现问题', health);
      }
      this.healthCheckTimer = 0;
    }
  }

  /**
   * 为数字人分配路径
   * @param entityId 实体ID
   * @param path 路径数据
   * @param options 跟随选项
   */
  public assignPathToAvatar(
    entityId: string,
    path: AvatarPath,
    options: {
      autoStart?: boolean;
      speedMultiplier?: number;
      loop?: boolean;
    } = {}
  ): boolean {
    try {
      const entity = this.world?.getEntity(entityId);
      if (!entity) {
        this.logger.warn('实体不存在', { entityId });
        return false;
      }

      // 检查是否有数字人组件
      const avatarComponent = entity.getComponent(EnhancedAvatarComponent.TYPE) as EnhancedAvatarComponent;
      if (!avatarComponent) {
        this.logger.warn('实体没有数字人组件', { entityId });
        return false;
      }

      // 检查是否已有路径跟随组件
      let pathComponent = entity.getComponent(AvatarPathFollowingComponent.TYPE) as AvatarPathFollowingComponent;
      
      if (!pathComponent) {
        // 创建新的路径跟随组件
        pathComponent = new AvatarPathFollowingComponent(entity, {
          path,
          autoStart: options.autoStart || false,
          speedMultiplier: options.speedMultiplier || 1.0,
          loop: options.loop || false,
          debug: this.systemOptions.debug
        });
        
        entity.addComponent(pathComponent);
        this.avatarPathComponents.set(entityId, pathComponent);
        
        // 设置事件监听
        this.setupComponentEvents(pathComponent, entityId);
      } else {
        // 更新现有组件的路径
        pathComponent.setPath(path);
        
        if (options.speedMultiplier !== undefined) {
          pathComponent.setSpeedMultiplier(options.speedMultiplier);
        }
        
        if (options.loop !== undefined) {
          pathComponent.setLoop(options.loop);
        }
        
        if (options.autoStart) {
          pathComponent.startFollowing();
        }
      }

      this.logger.info('为数字人分配路径', { entityId, pathId: path.id });
      this.eventEmitter.emit('pathAssigned', { entityId, pathId: path.id });
      
      return true;

    } catch (error) {
      this.logger.error('分配路径失败', { entityId, error });
      return false;
    }
  }

  /**
   * 开始数字人路径跟随
   * @param entityId 实体ID
   */
  public startAvatarPathFollowing(entityId: string): boolean {
    const component = this.avatarPathComponents.get(entityId);
    if (!component) {
      this.logger.warn('数字人没有路径跟随组件', { entityId });
      return false;
    }

    component.startFollowing();
    this.logger.info('开始数字人路径跟随', { entityId });
    return true;
  }

  /**
   * 停止数字人路径跟随
   * @param entityId 实体ID
   */
  public stopAvatarPathFollowing(entityId: string): boolean {
    const component = this.avatarPathComponents.get(entityId);
    if (!component) {
      return false;
    }

    component.stopFollowing();
    this.logger.info('停止数字人路径跟随', { entityId });
    return true;
  }

  /**
   * 暂停数字人路径跟随
   * @param entityId 实体ID
   */
  public pauseAvatarPathFollowing(entityId: string): boolean {
    const component = this.avatarPathComponents.get(entityId);
    if (!component) {
      return false;
    }

    component.pauseFollowing();
    this.logger.info('暂停数字人路径跟随', { entityId });
    return true;
  }

  /**
   * 恢复数字人路径跟随
   * @param entityId 实体ID
   */
  public resumeAvatarPathFollowing(entityId: string): boolean {
    const component = this.avatarPathComponents.get(entityId);
    if (!component) {
      return false;
    }

    component.resumeFollowing();
    this.logger.info('恢复数字人路径跟随', { entityId });
    return true;
  }

  /**
   * 批量操作数字人路径
   * @param entityIds 实体ID列表
   * @param operation 操作类型
   */
  public batchOperation(entityIds: string[], operation: 'start' | 'stop' | 'pause' | 'resume'): void {
    const results = entityIds.map(entityId => {
      switch (operation) {
        case 'start':
          return this.startAvatarPathFollowing(entityId);
        case 'stop':
          return this.stopAvatarPathFollowing(entityId);
        case 'pause':
          return this.pauseAvatarPathFollowing(entityId);
        case 'resume':
          return this.resumeAvatarPathFollowing(entityId);
        default:
          return false;
      }
    });

    const successCount = results.filter(r => r).length;
    this.logger.info('批量操作完成', { operation, total: entityIds.length, success: successCount });
  }

  /**
   * 获取数字人路径状态
   * @param entityId 实体ID
   */
  public getAvatarPathState(entityId: string): any {
    const component = this.avatarPathComponents.get(entityId);
    if (!component) {
      return null;
    }

    return {
      state: component.getPathFollowingState(),
      progress: component.getProgress(),
      entityId
    };
  }

  /**
   * 获取所有数字人路径状态
   */
  public getAllAvatarPathStates(): any[] {
    const states: any[] = [];
    
    this.avatarPathComponents.forEach((component, entityId) => {
      states.push({
        entityId,
        state: component.getPathFollowingState(),
        progress: component.getProgress()
      });
    });

    return states;
  }

  /**
   * 获取统计信息
   */
  public getStatistics(): AvatarPathStats {
    return { ...this.stats };
  }

  /**
   * 扫描现有实体
   */
  private scanExistingEntities(): void {
    const entities = this.world?.getEntities();
    if (entities) {
      entities.forEach(entity => {
        this.onEntityAdded(entity);
      });
    }
  }

  /**
   * 实体添加事件处理
   * @param entity 实体
   */
  private onEntityAdded(entity: Entity): void {
    // 检查是否有数字人组件
    const avatarComponent = entity.getComponent(EnhancedAvatarComponent.TYPE) as EnhancedAvatarComponent;
    if (avatarComponent) {
      this.avatarComponents.set(entity.id, avatarComponent);
    }

    // 检查是否有路径跟随组件
    const pathComponent = entity.getComponent(AvatarPathFollowingComponent.TYPE) as AvatarPathFollowingComponent;
    if (pathComponent) {
      this.avatarPathComponents.set(entity.id, pathComponent);
      this.setupComponentEvents(pathComponent, entity.id);
    }
  }

  /**
   * 实体移除事件处理
   * @param entity 实体
   */
  private onEntityRemoved(entity: Entity): void {
    this.avatarComponents.delete(entity.id);
    this.avatarPathComponents.delete(entity.id);
  }

  /**
   * 设置组件事件监听
   * @param component 路径跟随组件
   * @param entityId 实体ID
   */
  private setupComponentEvents(component: AvatarPathFollowingComponent, entityId: string): void {
    component.addEventListener('pathStarted', (data) => {
      this.eventEmitter.emit('avatarPathStarted', { entityId, ...data });
    });

    component.addEventListener('pathCompleted', (data) => {
      // 记录完成时间
      if (data.duration) {
        this.completionTimes.push(data.duration);
        if (this.completionTimes.length > this.MAX_COMPLETION_RECORDS) {
          this.completionTimes.shift();
        }
      }

      this.eventEmitter.emit('avatarPathCompleted', { entityId, ...data });
    });

    component.addEventListener('waypointReached', (data) => {
      this.eventEmitter.emit('avatarWaypointReached', { entityId, ...data });
    });

    component.addEventListener('triggerActivated', (data) => {
      this.eventEmitter.emit('avatarTriggerActivated', { entityId, ...data });
    });
  }

  /**
   * 更新统计信息
   */
  private updateStatistics(): void {
    this.stats.totalAvatars = this.avatarComponents.size;
    this.stats.followingAvatars = 0;
    this.stats.pausedAvatars = 0;
    this.stats.completedAvatars = 0;

    this.avatarPathComponents.forEach(component => {
      const state = component.getPathFollowingState();
      if (!state) return; // 处理 null 值

      switch (state) {
        case PathFollowingState.RUNNING:
          this.stats.followingAvatars++;
          break;
        case PathFollowingState.PAUSED:
          this.stats.pausedAvatars++;
          break;
        case PathFollowingState.COMPLETED:
          this.stats.completedAvatars++;
          break;
      }
    });

    // 计算平均完成时间
    if (this.completionTimes.length > 0) {
      const total = this.completionTimes.reduce((sum, time) => sum + time, 0);
      this.stats.averageCompletionTime = total / this.completionTimes.length;
    }
  }

  /**
   * 执行自动路径分配
   */
  private performAutoPathAssignment(): void {
    // 简单的自动分配逻辑
    // 实际项目中可以根据需求实现更复杂的分配策略

    this.avatarComponents.forEach((_, entityId) => {
      const pathComponent = this.avatarPathComponents.get(entityId);

      // 如果数字人没有路径且导航系统有可用路径
      if (!pathComponent && this.navigationPathSystem) {
        const availablePaths = this.navigationPathSystem.getAllGlobalPaths();
        const suitablePath = availablePaths.find((path: AvatarPath) => path.avatarId === entityId);

        if (suitablePath) {
          this.assignPathToAvatar(entityId, suitablePath, { autoStart: true });
        }
      }
    });
  }

  /**
   * 设置导航路径系统引用
   * @param navigationSystem 导航路径系统
   */
  public setNavigationPathSystem(navigationSystem: INavigationPathSystem): void {
    this.navigationPathSystem = navigationSystem;
    this.logger.info('设置导航路径系统引用');
  }

  /**
   * 路径跟随错误恢复
   * @param entityId 实体ID
   * @param error 错误信息
   */
  public handlePathFollowingError(entityId: string, error: any): void {
    const component = this.avatarPathComponents.get(entityId);
    if (!component) return;

    this.logger.error('路径跟随错误', { entityId, error });

    // 尝试重置路径
    try {
      component.resetPath();
      component.startFollowing();
      this.logger.info('路径跟随错误恢复成功', { entityId });
    } catch (recoveryError) {
      this.logger.error('路径跟随错误恢复失败', { entityId, recoveryError });
      // 停止有问题的路径跟随
      component.stopFollowing();
    }
  }

  /**
   * 优化数字人路径性能
   */
  public optimizePathPerformance(): void {
    if (!this.systemOptions.enablePathOptimization) return;

    let optimizedCount = 0;

    this.avatarPathComponents.forEach((component, entityId) => {
      const path = component.getPath();
      if (path && path.points.length > 10) {
        // 对复杂路径进行优化
        const originalCount = path.points.length;
        path.optimize(0.1);

        if (path.points.length < originalCount) {
          optimizedCount++;
          this.logger.debug('优化数字人路径', {
            entityId,
            originalCount,
            optimizedCount: path.points.length
          });
        }
      }
    });

    if (optimizedCount > 0) {
      this.logger.info('路径性能优化完成', { optimizedCount });
    }
  }

  /**
   * 获取系统健康状态
   */
  public getSystemHealth(): {
    status: 'healthy' | 'warning' | 'error';
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查组件数量
    if (this.avatarPathComponents.size > this.systemOptions.maxConcurrentFollowers!) {
      issues.push(`路径跟随组件数量超限: ${this.avatarPathComponents.size}/${this.systemOptions.maxConcurrentFollowers}`);
      recommendations.push('考虑增加最大并发数量限制或优化路径分配策略');
    }

    // 检查错误率
    const errorRate = this.stats.completedAvatars > 0 ?
      (this.stats.totalAvatars - this.stats.completedAvatars) / this.stats.totalAvatars : 0;

    if (errorRate > 0.1) {
      issues.push(`路径完成率较低: ${((1 - errorRate) * 100).toFixed(1)}%`);
      recommendations.push('检查路径配置和数字人组件状态');
    }

    // 检查平均完成时间
    if (this.stats.averageCompletionTime > 300) { // 5分钟
      issues.push(`平均路径完成时间过长: ${this.stats.averageCompletionTime.toFixed(1)}秒`);
      recommendations.push('考虑优化路径长度或增加移动速度');
    }

    const status = issues.length === 0 ? 'healthy' :
                  issues.length <= 2 ? 'warning' : 'error';

    return { status, issues, recommendations };
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 停止所有路径跟随
    this.avatarPathComponents.forEach(component => {
      component.stopFollowing();
    });

    // 清理数据
    this.avatarPathComponents.clear();
    this.avatarComponents.clear();
    this.completionTimes.length = 0;

    // 移除事件监听
    this.world?.off('entityAdded', this.onEntityAdded.bind(this));
    this.world?.off('entityRemoved', this.onEntityRemoved.bind(this));

    this.eventEmitter.removeAllListeners();
    super.dispose();

    this.logger.info('数字人路径系统已销毁');
  }
}
