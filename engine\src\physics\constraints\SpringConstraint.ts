/**
 * 弹簧约束
 * 在两个物体之间创建一个弹簧
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

/**
 * 弹簧约束选项
 */
export interface SpringConstraintOptions {
  /** 源物体上的连接点（局部坐标） */
  localAnchorA?: THREE.Vector3;
  /** 目标物体上的连接点（局部坐标） */
  localAnchorB?: THREE.Vector3;
  /** 弹簧静止长度 */
  restLength?: number;
  /** 弹簧刚度 */
  stiffness?: number;
  /** 阻尼系数 */
  damping?: number;
  /** 世界坐标系中的方向 */
  worldAxis?: THREE.Vector3;
  /** 是否允许连接的物体之间碰撞 */
  collideConnected?: boolean;
}

/**
 * 弹簧约束
 */
export class SpringConstraint extends PhysicsConstraint {
  /** 组件类型 */
  public static readonly type: string = 'SpringConstraint';

  /** 源物体上的连接点（局部坐标） */
  private localAnchorA: CANNON.Vec3;

  /** 目标物体上的连接点（局部坐标） */
  private localAnchorB: CANNON.Vec3;

  /** 弹簧静止长度 */
  private restLength: number;

  /** 弹簧刚度 */
  private stiffness: number;

  /** 阻尼系数 */
  private damping: number;

  /** 世界坐标系中的方向 */
  private worldAxis: CANNON.Vec3 | null = null;

  /** 最大力 */
  private maxForce: number;

  /**
   * 创建弹簧约束
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(targetEntity: Entity | null = null, options: SpringConstraintOptions = {}) {
    super(ConstraintType.SPRING, targetEntity, options);

    // 设置源物体上的连接点
    const localAnchorA = options.localAnchorA || new THREE.Vector3(0, 0, 0);
    this.localAnchorA = new CANNON.Vec3(localAnchorA.x, localAnchorA.y, localAnchorA.z);

    // 设置目标物体上的连接点
    const localAnchorB = options.localAnchorB || new THREE.Vector3(0, 0, 0);
    this.localAnchorB = new CANNON.Vec3(localAnchorB.x, localAnchorB.y, localAnchorB.z);

    // 设置弹簧参数
    this.restLength = options.restLength !== undefined ? options.restLength : 1;
    this.stiffness = options.stiffness !== undefined ? options.stiffness : 100;
    this.damping = options.damping !== undefined ? options.damping : 1;
    this.maxForce = 1e6; // 默认最大力

    // 设置世界坐标系中的方向
    if (options.worldAxis) {
      this.worldAxis = new CANNON.Vec3(options.worldAxis.x, options.worldAxis.y, options.worldAxis.z);
      this.worldAxis.normalize();
    }
  }

  /**
   * 创建约束
   */
  protected createConstraint(): void {
    // 获取源物体和目标物体
    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    // 如果没有源物体或目标物体，则无法创建约束
    if (!bodyA || !bodyB) {
      console.warn('无法创建弹簧约束：缺少源物体或目标物体');
      return;
    }

    // CANNON.js没有内置的Spring约束，使用DistanceConstraint作为替代
    this.constraint = new CANNON.DistanceConstraint(bodyA, bodyB, this.restLength, this.maxForce);
    this.constraint.collideConnected = this.collideConnected;
  }

  /**
   * 设置源物体上的连接点
   * @param anchor 连接点（局部坐标）
   */
  public setLocalAnchorA(anchor: THREE.Vector3): void {
    this.localAnchorA.set(anchor.x, anchor.y, anchor.z);

    // 注意：由于使用DistanceConstraint替代，锚点功能受限
  }

  /**
   * 获取源物体上的连接点
   * @returns 连接点（局部坐标）
   */
  public getLocalAnchorA(): THREE.Vector3 {
    return new THREE.Vector3(this.localAnchorA.x, this.localAnchorA.y, this.localAnchorA.z);
  }

  /**
   * 设置目标物体上的连接点
   * @param anchor 连接点（局部坐标）
   */
  public setLocalAnchorB(anchor: THREE.Vector3): void {
    this.localAnchorB.set(anchor.x, anchor.y, anchor.z);

    // 注意：由于使用DistanceConstraint替代，锚点功能受限
  }

  /**
   * 获取目标物体上的连接点
   * @returns 连接点（局部坐标）
   */
  public getLocalAnchorB(): THREE.Vector3 {
    return new THREE.Vector3(this.localAnchorB.x, this.localAnchorB.y, this.localAnchorB.z);
  }

  /**
   * 设置弹簧静止长度
   * @param length 静止长度
   */
  public setRestLength(length: number): void {
    this.restLength = length;

    // 注意：由于使用DistanceConstraint替代，需要重新创建约束来更新长度
    if (this.initialized && this.constraint && this.world) {
      this.world.removeConstraint(this.constraint);
      this.constraint = null;
      this.initialized = false;
      this.initialize(this.world);
    }
  }

  /**
   * 获取弹簧静止长度
   * @returns 静止长度
   */
  public getRestLength(): number {
    return this.restLength;
  }

  /**
   * 设置弹簧刚度
   * @param stiffness 刚度
   */
  public setStiffness(stiffness: number): void {
    this.stiffness = stiffness;

    // 注意：由于使用DistanceConstraint替代，刚度功能受限
  }

  /**
   * 获取弹簧刚度
   * @returns 刚度
   */
  public getStiffness(): number {
    return this.stiffness;
  }

  /**
   * 设置阻尼系数
   * @param damping 阻尼系数
   */
  public setDamping(damping: number): void {
    this.damping = damping;

    // 注意：由于使用DistanceConstraint替代，阻尼功能受限
  }

  /**
   * 获取阻尼系数
   * @returns 阻尼系数
   */
  public getDamping(): number {
    return this.damping;
  }

  /**
   * 设置世界坐标系中的方向
   * @param axis 方向
   */
  public setWorldAxis(axis: THREE.Vector3 | null): void {
    if (axis) {
      if (!this.worldAxis) {
        this.worldAxis = new CANNON.Vec3();
      }
      this.worldAxis.set(axis.x, axis.y, axis.z);
      this.worldAxis.normalize();
    } else {
      this.worldAxis = null;
    }

    // 注意：由于使用DistanceConstraint替代，世界轴功能受限
  }

  /**
   * 获取世界坐标系中的方向
   * @returns 方向
   */
  public getWorldAxis(): THREE.Vector3 | null {
    if (!this.worldAxis) return null;
    return new THREE.Vector3(this.worldAxis.x, this.worldAxis.y, this.worldAxis.z);
  }

  /**
   * 应用约束
   * 弹簧约束需要在每一帧手动应用
   */
  public applyConstraint(): void {
    // 注意：由于使用DistanceConstraint替代，不需要手动应用力
  }

  /**
   * 更新约束
   * 在物理系统的每一帧调用
   */
  public update(): void {
    // 注意：由于使用DistanceConstraint替代，不需要手动更新
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new SpringConstraint(this.targetEntity, {
      localAnchorA: new THREE.Vector3(this.localAnchorA.x, this.localAnchorA.y, this.localAnchorA.z),
      localAnchorB: new THREE.Vector3(this.localAnchorB.x, this.localAnchorB.y, this.localAnchorB.z),
      restLength: this.restLength,
      stiffness: this.stiffness,
      damping: this.damping,
      worldAxis: this.worldAxis ? new THREE.Vector3(this.worldAxis.x, this.worldAxis.y, this.worldAxis.z) : undefined,
      collideConnected: this.collideConnected
    });
  }
}
