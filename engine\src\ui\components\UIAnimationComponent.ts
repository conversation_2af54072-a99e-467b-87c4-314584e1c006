/**
 * UIAnimationComponent.ts
 *
 * UI动画组件，用于为UI元素添加动画效果
 */

import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector2, Vector3, Color } from 'three';
import { IUIAnimation, UIAnimationType, UIEasingFunction } from '../interfaces/IUIElement';
import { UIComponent } from './UIComponent';

/**
 * 动画目标属性类型
 */
export type AnimationTargetProperty =
  'position' | 'size' | 'opacity' | 'rotation' | 'scale' |
  'color' | 'backgroundColor' | 'borderColor' | 'fontColor';

/**
 * 动画缓动函数
 */
export class UIEasing {
  // 线性
  static linear: UIEasingFunction = (t) => t;

  // 二次方
  static quadIn: UIEasingFunction = (t) => t * t;
  static quadOut: UIEasingFunction = (t) => t * (2 - t);
  static quadInOut: UIEasingFunction = (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;

  // 三次方
  static cubicIn: UIEasingFunction = (t) => t * t * t;
  static cubicOut: UIEasingFunction = (t) => (--t) * t * t + 1;
  static cubicInOut: UIEasingFunction = (t) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;

  // 四次方
  static quartIn: UIEasingFunction = (t) => t * t * t * t;
  static quartOut: UIEasingFunction = (t) => 1 - (--t) * t * t * t;
  static quartInOut: UIEasingFunction = (t) => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t;

  // 指数
  static expoIn: UIEasingFunction = (t) => t === 0 ? 0 : Math.pow(2, 10 * (t - 1));
  static expoOut: UIEasingFunction = (t) => t === 1 ? 1 : 1 - Math.pow(2, -10 * t);
  static expoInOut: UIEasingFunction = (t) => {
    if (t === 0) return 0;
    if (t === 1) return 1;
    if ((t *= 2) < 1) return 0.5 * Math.pow(2, 10 * (t - 1));
    return 0.5 * (2 - Math.pow(2, -10 * (t - 1)));
  };

  // 正弦
  static sineIn: UIEasingFunction = (t) => 1 - Math.cos(t * Math.PI / 2);
  static sineOut: UIEasingFunction = (t) => Math.sin(t * Math.PI / 2);
  static sineInOut: UIEasingFunction = (t) => 0.5 * (1 - Math.cos(Math.PI * t));

  // 弹性
  static elasticIn: UIEasingFunction = (t) => {
    if (t === 0) return 0;
    if (t === 1) return 1;
    return -Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI);
  };

  static elasticOut: UIEasingFunction = (t) => {
    if (t === 0) return 0;
    if (t === 1) return 1;
    return Math.pow(2, -10 * t) * Math.sin((t - 0.1) * 5 * Math.PI) + 1;
  };

  static elasticInOut: UIEasingFunction = (t) => {
    if (t === 0) return 0;
    if (t === 1) return 1;
    t *= 2;
    if (t < 1) {
      return -0.5 * Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI);
    }
    return 0.5 * Math.pow(2, -10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI) + 1;
  };

  // 反弹
  static bounceIn: UIEasingFunction = (t) => 1 - UIEasing.bounceOut(1 - t);

  static bounceOut: UIEasingFunction = (t) => {
    if (t < 1 / 2.75) {
      return 7.5625 * t * t;
    } else if (t < 2 / 2.75) {
      return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
    } else if (t < 2.5 / 2.75) {
      return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
    } else {
      return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
    }
  };

  static bounceInOut: UIEasingFunction = (t) => {
    if (t < 0.5) return UIEasing.bounceIn(t * 2) * 0.5;
    return UIEasing.bounceOut(t * 2 - 1) * 0.5 + 0.5;
  };
}

/**
 * UI动画类
 * 实现IUIAnimation接口
 */
export class UIAnimation implements IUIAnimation {
  type: UIAnimationType;
  target: UIComponent;
  duration: number;
  delay: number;
  easing: UIEasingFunction;
  loop: boolean;
  from: any;
  to: any;
  progress: number = 0;

  // 额外属性
  property: AnimationTargetProperty;
  startTime: number = 0;
  isPlaying: boolean = false;
  isPaused: boolean = false;
  isCompleted: boolean = false;
  onComplete?: () => void;
  onUpdate?: (value: any) => void;

  /**
   * 构造函数
   * @param type 动画类型
   * @param target 目标UI元素
   * @param property 目标属性
   * @param from 起始值
   * @param to 结束值
   * @param duration 持续时间（毫秒）
   * @param options 其他选项
   */
  constructor(
    type: UIAnimationType,
    target: UIComponent,
    property: AnimationTargetProperty,
    from: any,
    to: any,
    duration: number = 1000,
    options: {
      delay?: number,
      easing?: UIEasingFunction,
      loop?: boolean,
      onComplete?: () => void,
      onUpdate?: (value: any) => void
    } = {}
  ) {
    this.type = type;
    this.target = target;
    this.property = property;
    this.from = from;
    this.to = to;
    this.duration = duration;
    this.delay = options.delay || 0;
    this.easing = options.easing || UIEasing.linear;
    this.loop = options.loop || false;
    this.onComplete = options.onComplete;
    this.onUpdate = options.onUpdate;
  }

  /**
   * 开始动画
   */
  start(): void {
    this.startTime = Date.now() + this.delay;
    this.isPlaying = true;
    this.isPaused = false;
    this.isCompleted = false;
    this.progress = 0;
  }

  /**
   * 暂停动画
   */
  pause(): void {
    if (this.isPlaying) {
      this.isPaused = true;
      this.isPlaying = false;
    }
  }

  /**
   * 恢复动画
   */
  resume(): void {
    if (this.isPaused) {
      this.isPaused = false;
      this.isPlaying = true;
      // 调整开始时间，以便从当前进度继续
      this.startTime = Date.now() - (this.progress * this.duration);
    }
  }

  /**
   * 停止动画
   */
  stop(): void {
    this.isPlaying = false;
    this.isPaused = false;
    this.isCompleted = false;
    this.progress = 0;
  }

  /**
   * 更新动画
   * @param _deltaTime 时间增量 - 未使用，使用 Date.now() 代替
   */
  update(_deltaTime: number): void {
    if (!this.isPlaying || this.isPaused || this.isCompleted) return;

    const currentTime = Date.now();

    // 如果还在延迟中，则不更新
    if (currentTime < this.startTime) return;

    // 计算进度
    const elapsedTime = currentTime - this.startTime;
    this.progress = Math.min(elapsedTime / this.duration, 1);

    // 应用缓动函数
    const easedProgress = this.easing(this.progress);

    // 计算当前值
    const currentValue = this.interpolate(this.from, this.to, easedProgress);

    // 应用到目标
    this.applyValue(currentValue);

    // 调用更新回调
    if (this.onUpdate) {
      this.onUpdate(currentValue);
    }

    // 检查是否完成
    if (this.progress >= 1) {
      if (this.loop) {
        // 如果循环，则重置开始时间
        this.startTime = currentTime;
        this.progress = 0;
      } else {
        // 否则标记为完成
        this.isPlaying = false;
        this.isCompleted = true;

        // 调用完成回调
        if (this.onComplete) {
          this.onComplete();
        }
      }
    }
  }

  /**
   * 插值计算
   * @param from 起始值
   * @param to 结束值
   * @param progress 进度（0-1）
   * @returns 插值结果
   */
  private interpolate(from: any, to: any, progress: number): any {
    // 数字插值
    if (typeof from === 'number' && typeof to === 'number') {
      return from + (to - from) * progress;
    }

    // Vector2插值
    if (from instanceof Vector2 && to instanceof Vector2) {
      return new Vector2().lerpVectors(from, to, progress);
    }

    // Vector3插值
    if (from instanceof Vector3 && to instanceof Vector3) {
      return new Vector3().lerpVectors(from, to, progress);
    }

    // 颜色插值
    if ((from instanceof Color || typeof from === 'string') &&
        (to instanceof Color || typeof to === 'string')) {
      const fromColor = from instanceof Color ? from : new Color(from);
      const toColor = to instanceof Color ? to : new Color(to);
      return new Color().lerpColors(fromColor, toColor, progress);
    }

    // 默认返回目标值
    return progress < 1 ? from : to;
  }

  /**
   * 应用值到目标
   * @param value 要应用的值
   */
  private applyValue(value: any): void {
    switch (this.property) {
      case 'position':
        this.target.setPosition(value);
        break;
      case 'size':
        this.target.setSize(value);
        break;
      case 'opacity':
        this.target.setOpacity(value);
        break;
      case 'rotation':
        if (this.target.is3D) {
          (this.target as any).setRotation(value);
        }
        break;
      case 'scale':
        if (this.target.is3D) {
          (this.target as any).setScale(value);
        }
        break;
      case 'color':
        if (this.target.is3D) {
          (this.target as any).setFontColor(value.getStyle());
        }
        break;
      case 'backgroundColor':
        this.target.backgroundColor = value.getStyle();
        break;
      case 'borderColor':
        this.target.borderColor = value.getStyle();
        break;
      case 'fontColor':
        if (!this.target.is3D) {
          (this.target as any).textColor = value.getStyle();
        } else {
          (this.target as any).fontColor = value.getStyle();
        }
        break;
    }
  }
}

/**
 * UI动画组件
 * 用于管理实体的UI动画
 */
export class UIAnimationComponent extends Component {
  // 动画列表
  animations: UIAnimation[] = [];

  /**
   * 构造函数
   * @param entity 关联的实体
   */
  constructor(entity: Entity) {
    // 调用基类构造函数，传入组件类型名称
    super('UIAnimation');
    // 设置实体引用
    this.setEntity(entity);
  }

  /**
   * 添加动画
   * @param animation 要添加的动画
   * @returns 添加的动画
   */
  addAnimation(animation: UIAnimation): UIAnimation {
    this.animations.push(animation);
    return animation;
  }

  /**
   * 移除动画
   * @param animation 要移除的动画
   */
  removeAnimation(animation: UIAnimation): void {
    const index = this.animations.indexOf(animation);
    if (index !== -1) {
      this.animations.splice(index, 1);
    }
  }

  /**
   * 清除所有动画
   */
  clearAnimations(): void {
    this.animations = [];
  }

  /**
   * 更新所有动画
   * @param deltaTime 时间增量（秒）
   */
  update(deltaTime: number): void {
    // 将秒转换为毫秒
    const deltaMilliseconds = deltaTime * 1000;

    // 更新所有动画
    for (let i = this.animations.length - 1; i >= 0; i--) {
      const animation = this.animations[i];
      animation.update(deltaMilliseconds);

      // 如果动画已完成且不循环，则移除
      if (animation.isCompleted && !animation.loop) {
        this.animations.splice(i, 1);
      }
    }
  }

  /**
   * 创建并添加动画
   * @param type 动画类型
   * @param target 目标UI元素
   * @param property 目标属性
   * @param from 起始值
   * @param to 结束值
   * @param duration 持续时间（毫秒）
   * @param options 其他选项
   * @returns 创建的动画
   */
  createAnimation(
    type: UIAnimationType,
    target: UIComponent,
    property: AnimationTargetProperty,
    from: any,
    to: any,
    duration: number = 1000,
    options: {
      delay?: number,
      easing?: UIEasingFunction,
      loop?: boolean,
      onComplete?: () => void,
      onUpdate?: (value: any) => void
    } = {}
  ): UIAnimation {
    const animation = new UIAnimation(type, target, property, from, to, duration, options);
    this.addAnimation(animation);
    return animation;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    const newComponent = new UIAnimationComponent();
    // 复制动画列表
    for (const animation of this.animations) {
      newComponent.addAnimation(animation);
    }
    return newComponent;
  }
}
