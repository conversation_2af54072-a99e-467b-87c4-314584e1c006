/**
 * 固定约束
 * 将两个物体固定在相对位置和旋转
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import type { PhysicsBody } from '../PhysicsBody';
import { Component } from '../../core/Component';
import { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

/**
 * 固定约束选项
 */
export interface FixedConstraintOptions {
  /** 是否允许连接的物体之间碰撞 */
  collideConnected?: boolean;
  /** 最大力 */
  maxForce?: number;
}

/**
 * 固定约束
 */
export class FixedConstraint extends PhysicsConstraint {
  /** 组件类型 */
  public static readonly type: string = 'FixedConstraint';

  /** 最大力 */
  private maxForce: number;

  /**
   * 创建固定约束
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(targetEntity: Entity | null = null, options: FixedConstraintOptions = {}) {
    super(ConstraintType.FIXED, targetEntity, options);

    // 设置最大力
    this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
  }

  /**
   * 创建约束
   */
  protected createConstraint(): void {
    // 获取源物体和目标物体
    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    // 如果没有源物体或目标物体，则无法创建约束
    if (!bodyA || !bodyB) {
      console.warn('无法创建固定约束：缺少源物体或目标物体');
      return;
    }

    // 创建固定约束
    // 注意：CANNON.js没有内置的固定约束，我们使用锁定约束实现
    // LockConstraint会锁定两个物体的相对位置和旋转
    this.constraint = new CANNON.LockConstraint(bodyA, bodyB);

    // 设置最大力（如果约束支持的话）
    if ('maxForce' in this.constraint) {
      (this.constraint as any).maxForce = this.maxForce;
    }

    // 设置是否允许连接的物体之间碰撞
    this.constraint.collideConnected = this.collideConnected;
  }

  /**
   * 设置最大力
   * @param maxForce 最大力
   */
  public setMaxForce(maxForce: number): void {
    this.maxForce = maxForce;

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取最大力
   * @returns 最大力
   */
  public getMaxForce(): number {
    return this.maxForce;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new FixedConstraint(this.targetEntity, {
      maxForce: this.maxForce,
      collideConnected: this.collideConnected
    });
  }

  /**
   * 重新创建约束
   */
  private recreateConstraint(): void {
    if (this.initialized && this.constraint && this.world) {
      this.world.removeConstraint(this.constraint);
      this.constraint = null;
      this.initialized = false;
      this.initialize(this.world);
    }
  }
}
