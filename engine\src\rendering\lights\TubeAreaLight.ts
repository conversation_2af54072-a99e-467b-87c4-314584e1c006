/**
 * 管状区域光
 * 物理精确的管状区域光
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { AreaLight, AreaLightOptions, AreaLightType } from './AreaLight';

/**
 * 管状区域光选项接口
 */
export interface TubeAreaLightOptions extends AreaLightOptions {
  /** 光源类型 */
  type: AreaLightType.TUBE;
  /** 长度 */
  length?: number;
  /** 半径 */
  radius?: number;
  /** 是否使用物理单位 */
  usePhysicalUnits?: boolean;
  /** 功率（瓦特） */
  power?: number;
  /** 发光效率（流明/瓦特） */
  efficacy?: number;
  /** 辅助对象细分数 */
  helperSegments?: number;
}

/**
 * 管状区域光组件类
 */
export class TubeAreaLightComponent extends AreaLight {
  /** 长度 */
  private length: number;

  /** 半径 */
  private radius: number;

  /** 是否使用物理单位 */
  private usePhysicalUnits: boolean;

  /** 功率（瓦特） */
  private power: number;

  /** 发光效率（流明/瓦特） */
  private efficacy: number;

  /** 辅助对象细分数 */
  private helperSegments: number;

  /** 辅助对象材质 */
  private helperMaterial: THREE.MeshBasicMaterial | null = null;

  /**
   * 创建管状区域光组件
   * @param options 管状区域光选项
   */
  constructor(options: TubeAreaLightOptions) {
    super(options);

    this.length = options.length !== undefined ? options.length : 5;
    this.radius = options.radius !== undefined ? options.radius : 0.5;
    this.usePhysicalUnits = options.usePhysicalUnits !== undefined ? options.usePhysicalUnits : false;
    this.power = options.power !== undefined ? options.power : 60;
    this.efficacy = options.efficacy !== undefined ? options.efficacy : 80;
    this.helperSegments = options.helperSegments !== undefined ? options.helperSegments : 16;

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 创建光源
   * @param options 管状区域光选项
   * @returns Three.js线性光源（使用多个点光源模拟）
   */
  protected createLight(options: TubeAreaLightOptions): THREE.Group {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const castShadow = options.castShadow !== undefined ? options.castShadow : false;

    // 创建光源组
    const group = new THREE.Group();

    // 创建多个点光源模拟管状光源
    const lightCount = 3; // 使用3个点光源
    const lightIntensity = intensity / lightCount; // 平均分配强度

    for (let i = 0; i < lightCount; i++) {
      const t = i / (lightCount - 1); // 0 到 1
      const x = (t - 0.5) * this.length; // 在长度上均匀分布

      const light = new THREE.PointLight(color, lightIntensity, 0, 2);
      light.position.set(x, 0, 0);
      light.castShadow = castShadow;

      if (light.castShadow) {
        // 设置阴影贴图大小
        light.shadow.mapSize.width = 1024;
        light.shadow.mapSize.height = 1024;

        // 设置阴影相机
        light.shadow.camera.near = 0.1;
        light.shadow.camera.far = 500;

        // 设置阴影偏移
        light.shadow.bias = -0.0005;
      }

      group.add(light);
    }

    return group;
  }

  /**
   * 创建辅助对象
   * @returns Three.js管状区域光辅助对象
   */
  protected createHelper(): THREE.Object3D {
    // 创建圆柱体几何体
    const geometry = new THREE.CylinderGeometry(
      this.radius,
      this.radius,
      this.length,
      this.helperSegments,
      1,
      true
    );

    // 旋转几何体，使其沿X轴
    geometry.rotateZ(Math.PI / 2);

    // 创建材质
    this.helperMaterial = new THREE.MeshBasicMaterial({
      color: this.helperColor,
      wireframe: true,
      transparent: true,
      opacity: 0.5,
      side: THREE.DoubleSide
    });

    // 创建网格
    const mesh = new THREE.Mesh(geometry, this.helperMaterial);
    mesh.visible = this.showHelper;

    return mesh;
  }

  /**
   * 更新辅助对象颜色
   */
  protected updateHelperColor(): void {
    if (this.helperMaterial) {
      this.helperMaterial.color.copy(this.helperColor);
    }
  }

  /**
   * 更新物理强度
   */
  private updatePhysicalIntensity(): void {
    if (!this.usePhysicalUnits) return;

    // 计算流明
    const lumens = this.power * this.efficacy;

    // 计算表面积
    const surfaceArea = 2 * Math.PI * this.radius * this.length;

    // 计算强度（流明/表面积）
    const intensity = lumens / surfaceArea;

    // 设置光源强度
    if (this.light instanceof THREE.Group) {
      const lightCount = this.light.children.length;
      const lightIntensity = intensity / lightCount;

      for (const child of this.light.children) {
        if (child instanceof THREE.PointLight) {
          child.intensity = lightIntensity;
        }
      }
    }
  }

  /**
   * 设置长度
   * @param length 长度
   */
  public setLength(length: number): void {
    this.length = length;

    // 更新光源位置
    this.updateLightPositions();

    // 更新辅助对象
    this.updateHelper();

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取长度
   * @returns 长度
   */
  public getLength(): number {
    return this.length;
  }

  /**
   * 设置半径
   * @param radius 半径
   */
  public setRadius(radius: number): void {
    this.radius = radius;

    // 更新辅助对象
    this.updateHelper();

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取半径
   * @returns 半径
   */
  public getRadius(): number {
    return this.radius;
  }

  /**
   * 设置是否使用物理单位
   * @param use 是否使用
   */
  public setUsePhysicalUnits(use: boolean): void {
    this.usePhysicalUnits = use;

    // 如果使用物理单位，则更新光源强度
    if (use) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取是否使用物理单位
   * @returns 是否使用
   */
  public isUsePhysicalUnits(): boolean {
    return this.usePhysicalUnits;
  }

  /**
   * 设置功率
   * @param power 功率（瓦特）
   */
  public setPower(power: number): void {
    this.power = power;

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取功率
   * @returns 功率（瓦特）
   */
  public getPower(): number {
    return this.power;
  }

  /**
   * 设置发光效率
   * @param efficacy 发光效率（流明/瓦特）
   */
  public setEfficacy(efficacy: number): void {
    this.efficacy = efficacy;

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取发光效率
   * @returns 发光效率（流明/瓦特）
   */
  public getEfficacy(): number {
    return this.efficacy;
  }

  /**
   * 设置辅助对象细分数
   * @param segments 细分数
   */
  public setHelperSegments(segments: number): void {
    this.helperSegments = segments;

    // 更新辅助对象
    this.updateHelper();
  }

  /**
   * 获取辅助对象细分数
   * @returns 细分数
   */
  public getHelperSegments(): number {
    return this.helperSegments;
  }

  /**
   * 更新光源位置
   */
  private updateLightPositions(): void {
    if (this.light instanceof THREE.Group) {
      const lightCount = this.light.children.length;

      for (let i = 0; i < lightCount; i++) {
        const t = i / (lightCount - 1); // 0 到 1
        const x = (t - 0.5) * this.length; // 在长度上均匀分布

        const light = this.light.children[i];
        if (light instanceof THREE.PointLight) {
          light.position.set(x, 0, 0);
        }
      }
    }
  }

  /**
   * 更新辅助对象
   */
  private updateHelper(): void {
    if (this.helper) {
      // 更新辅助对象
      const parent = this.helper.parent;

      if (parent) {
        parent.remove(this.helper);
      }

      // 如果辅助对象有dispose方法，则调用
      if (this.helper instanceof THREE.Mesh && this.helper.geometry) {
        (this.helper.geometry as any).dispose();
      }

      this.helper = this.createHelper();

      if (parent) {
        parent.add(this.helper);
      }
    }
  }

  /**
   * 更新组件
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 管状区域光不需要每帧更新
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    // 获取当前光源的属性
    let color = 0xffffff;
    let intensity = 1;
    let castShadow = false;

    if (this.light instanceof THREE.Group && this.light.children.length > 0) {
      const firstLight = this.light.children[0];
      if (firstLight instanceof THREE.PointLight) {
        color = firstLight.color.getHex();
        intensity = firstLight.intensity * this.light.children.length; // 总强度
        castShadow = firstLight.castShadow;
      }
    }

    return new TubeAreaLightComponent({
      type: AreaLightType.TUBE,
      color: color,
      intensity: intensity,
      castShadow: castShadow,
      showHelper: this.showHelper,
      helperColor: this.helperColor.getHex(),
      length: this.length,
      radius: this.radius,
      usePhysicalUnits: this.usePhysicalUnits,
      power: this.power,
      efficacy: this.efficacy,
      helperSegments: this.helperSegments
    });
  }
}
