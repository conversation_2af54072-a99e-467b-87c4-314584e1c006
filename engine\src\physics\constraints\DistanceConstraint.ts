/**
 * 距离约束
 * 保持两个物体之间的距离
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

/**
 * 距离约束选项
 */
export interface DistanceConstraintOptions {
  /** 距离 */
  distance?: number;
  /** 最大力 */
  maxForce?: number;
  /** 是否允许连接的物体之间碰撞 */
  collideConnected?: boolean;
}

/**
 * 距离约束
 */
export class DistanceConstraint extends PhysicsConstraint {
  /** 组件类型 */
  public static readonly type: string = 'DistanceConstraint';

  /** 距离 */
  private distance: number;

  /** 最大力 */
  private maxForce: number;

  /**
   * 创建距离约束
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(targetEntity: Entity | null = null, options: DistanceConstraintOptions = {}) {
    super(ConstraintType.DISTANCE, targetEntity, options);

    // 设置距离
    this.distance = options.distance !== undefined ? options.distance : 1;

    // 设置最大力
    this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
  }

  /**
   * 创建约束
   */
  protected createConstraint(): void {
    // 获取源物体和目标物体
    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    // 如果没有源物体或目标物体，则无法创建约束
    if (!bodyA || !bodyB) {
      console.warn('无法创建距离约束：缺少源物体或目标物体');
      return;
    }

    // 创建距离约束
    this.constraint = new CANNON.DistanceConstraint(
      bodyA,
      bodyB,
      this.distance,
      this.maxForce
    );

    // 设置是否允许连接的物体之间碰撞
    this.constraint.collideConnected = this.collideConnected;
  }

  /**
   * 设置距离
   * @param distance 距离
   */
  public setDistance(distance: number): void {
    this.distance = distance;

    // 如果约束已创建，更新约束
    if (this.constraint instanceof CANNON.DistanceConstraint) {
      this.constraint.distance = distance;
    }
  }

  /**
   * 获取距离
   * @returns 距离
   */
  public getDistance(): number {
    return this.distance;
  }

  /**
   * 设置最大力
   * @param maxForce 最大力
   */
  public setMaxForce(maxForce: number): void {
    this.maxForce = maxForce;

    // 如果约束已创建，更新约束
    if (this.constraint instanceof CANNON.DistanceConstraint) {
      this.constraint.equations[0].maxForce = maxForce;
      this.constraint.equations[0].minForce = -maxForce;
    }
  }

  /**
   * 获取最大力
   * @returns 最大力
   */
  public getMaxForce(): number {
    return this.maxForce;
  }

  /**
   * 获取当前距离
   * @returns 当前距离
   */
  public getCurrentDistance(): number {
    if (!this.initialized || !this.constraint) return this.distance;

    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    if (!bodyA || !bodyB) return this.distance;

    const posA = bodyA.position;
    const posB = bodyB.position;

    return Math.sqrt(
      Math.pow(posB.x - posA.x, 2) +
      Math.pow(posB.y - posA.y, 2) +
      Math.pow(posB.z - posA.z, 2)
    );
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new DistanceConstraint(this.targetEntity, {
      distance: this.distance,
      maxForce: this.maxForce,
      collideConnected: this.collideConnected
    });
  }
}
