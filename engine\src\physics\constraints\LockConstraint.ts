/**
 * 锁定约束
 * 锁定两个物体之间的相对位置和旋转
 */
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

/**
 * 锁定约束选项
 */
export interface LockConstraintOptions {
  /** 最大力 */
  maxForce?: number;
  /** 是否允许连接的物体之间碰撞 */
  collideConnected?: boolean;
}

/**
 * 锁定约束
 */
export class LockConstraint extends PhysicsConstraint {
  /** 组件类型 */
  public static readonly type: string = 'LockConstraint';

  /** 最大力 */
  private maxForce: number;

  /**
   * 创建锁定约束
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(targetEntity: Entity | null = null, options: LockConstraintOptions = {}) {
    super(ConstraintType.LOCK, targetEntity, options);

    // 设置最大力
    this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
  }

  /**
   * 创建约束
   */
  protected createConstraint(): void {
    // 获取源物体和目标物体
    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    // 如果没有源物体或目标物体，则无法创建约束
    if (!bodyA || !bodyB) {
      console.warn('无法创建锁定约束：缺少源物体或目标物体');
      return;
    }

    // 创建锁定约束
    this.constraint = new CANNON.LockConstraint(bodyA, bodyB, {
      maxForce: this.maxForce
    });

    // 设置是否允许连接的物体之间碰撞
    this.constraint.collideConnected = this.collideConnected;
  }

  /**
   * 设置最大力
   * @param maxForce 最大力
   */
  public setMaxForce(maxForce: number): void {
    this.maxForce = maxForce;

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取最大力
   * @returns 最大力
   */
  public getMaxForce(): number {
    return this.maxForce;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new LockConstraint(this.targetEntity, {
      maxForce: this.maxForce,
      collideConnected: this.collideConnected
    });
  }

  /**
   * 重新创建约束
   */
  private recreateConstraint(): void {
    if (this.initialized && this.constraint && this.world) {
      this.world.removeConstraint(this.constraint);
      this.constraint = null;
      this.initialized = false;
      this.initialize(this.world);
    }
  }
}
