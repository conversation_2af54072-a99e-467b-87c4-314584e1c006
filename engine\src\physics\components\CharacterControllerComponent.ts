/**
 * 角色控制器组件
 * 为实体提供角色控制器功能
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { CharacterController, CharacterControllerOptions } from '../character/CharacterController';

/**
 * 角色控制器组件
 */
export class CharacterControllerComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'CharacterControllerComponent';

  /** 角色控制器 */
  private controller: CharacterController | null = null;

  /** 控制器选项 */
  private options: CharacterControllerOptions;

  /** 物理世界 */
  private world: CANNON.World | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /** 计算出的移动向量 */
  private computedMovement: THREE.Vector3 = new THREE.Vector3();

  /**
   * 创建角色控制器组件
   * @param options 控制器选项
   */
  constructor(options: CharacterControllerOptions = {}) {
    super(CharacterControllerComponent.type);
    this.options = options;
  }

  /**
   * 初始化角色控制器
   * @param world 物理世界
   */
  public initialize(world: CANNON.World): void {
    if (this.initialized || !this.entity || this.destroyed) return;

    this.world = world;

    // 创建角色控制器
    this.controller = new CharacterController(this.entity, world, this.options);

    this.initialized = true;
  }

  /**
   * 计算碰撞器移动
   * @param desiredTranslation 期望的移动向量
   * @param filterGroups 碰撞组过滤
   * @param filterPredicate 碰撞过滤谓词函数
   */
  public computeColliderMovement(
    desiredTranslation: THREE.Vector3,
    filterGroups?: number,
    filterPredicate?: (body: CANNON.Body) => boolean
  ): void {
    if (!this.controller) return;

    this.controller.computeColliderMovement(desiredTranslation, filterGroups, filterPredicate);
  }

  /**
   * 获取计算出的移动向量
   * @returns 计算出的移动向量
   */
  public getComputedMovement(): THREE.Vector3 {
    if (!this.controller) return new THREE.Vector3();

    return this.controller.getComputedMovement();
  }

  /**
   * 获取偏移量
   * @returns 偏移量
   */
  public getOffset(): number {
    if (!this.controller) return 0;

    return this.controller.getOffset();
  }

  /**
   * 设置最大爬坡角度
   * @param angle 角度（弧度）
   */
  public setMaxSlopeClimbAngle(angle: number): void {
    if (!this.controller) return;

    this.controller.setMaxSlopeClimbAngle(angle);
  }

  /**
   * 获取最大爬坡角度
   * @returns 角度（弧度）
   */
  public getMaxSlopeClimbAngle(): number {
    if (!this.controller) return 0;

    return this.controller.getMaxSlopeClimbAngle();
  }

  /**
   * 设置最小滑坡角度
   * @param angle 角度（弧度）
   */
  public setMinSlopeSlideAngle(angle: number): void {
    if (!this.controller) return;

    this.controller.setMinSlopeSlideAngle(angle);
  }

  /**
   * 获取最小滑坡角度
   * @returns 角度（弧度）
   */
  public getMinSlopeSlideAngle(): number {
    if (!this.controller) return 0;

    return this.controller.getMinSlopeSlideAngle();
  }

  /**
   * 启用自动台阶
   * @param maxHeight 最大台阶高度
   * @param minWidth 最小台阶宽度
   * @param stepOverDynamic 是否可以踏上动态物体
   */
  public enableAutoStep(maxHeight: number, minWidth: number, stepOverDynamic: boolean): void {
    if (!this.controller) return;

    this.controller.enableAutoStep(maxHeight, minWidth, stepOverDynamic);
  }

  /**
   * 禁用自动台阶
   */
  public disableAutoStep(): void {
    if (!this.controller) return;

    this.controller.disableAutoStep();
  }

  /**
   * 启用地面吸附
   * @param distance 吸附距离
   */
  public enableSnapToGround(distance: number): void {
    if (!this.controller) return;

    this.controller.enableSnapToGround(distance);
  }

  /**
   * 禁用地面吸附
   */
  public disableSnapToGround(): void {
    if (!this.controller) return;

    this.controller.disableSnapToGround();
  }

  /**
   * 是否启用自动台阶
   * @returns 是否启用
   */
  public isAutoStepEnabled(): boolean {
    if (!this.controller) return false;

    return this.controller.isAutoStepEnabled();
  }

  /**
   * 是否启用地面吸附
   * @returns 是否启用
   */
  public isSnapToGroundEnabled(): boolean {
    if (!this.controller) return false;

    return this.controller.isSnapToGroundEnabled();
  }

  /**
   * 获取自动台阶最大高度
   * @returns 最大高度
   */
  public getAutoStepMaxHeight(): number {
    if (!this.controller) return 0;

    return this.controller.getAutoStepMaxHeight();
  }

  /**
   * 获取自动台阶最小宽度
   * @returns 最小宽度
   */
  public getAutoStepMinWidth(): number {
    if (!this.controller) return 0;

    return this.controller.getAutoStepMinWidth();
  }

  /**
   * 是否可以踏上动态物体
   * @returns 是否可以
   */
  public canStepOverDynamic(): boolean {
    if (!this.controller) return false;

    return this.controller.canStepOverDynamic();
  }

  /**
   * 是否在地面上
   * @returns 是否在地面上
   */
  public isOnGround(): boolean {
    if (!this.controller) return false;

    return this.controller.isOnGround();
  }

  /**
   * 获取地面法线
   * @returns 地面法线
   */
  public getGroundNormal(): THREE.Vector3 {
    if (!this.controller) return new THREE.Vector3(0, 1, 0);

    return this.controller.getGroundNormal();
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new CharacterControllerComponent({ ...this.options });
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    if (this.destroyed) return;

    this.controller = null;
    this.world = null;
    this.initialized = false;
    this.destroyed = true;

    super.dispose();
  }
}
