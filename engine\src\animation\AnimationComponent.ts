/**
 * 动画组件
 * 基础动画组件类
 */

import { Component } from '../core/Component';
import { Animator } from './Animator';
import { AnimationClip, AnimationEvent, AnimationEventType } from './AnimationClip';
import { EventEmitter, EventCallback } from '../utils/EventEmitter';
import * as THREE from 'three';

/**
 * 动画播放状态
 */
export enum AnimationState {
  /** 停止 */
  STOPPED = 'stopped',
  /** 播放中 */
  PLAYING = 'playing',
  /** 暂停 */
  PAUSED = 'paused',
  /** 混合中 */
  BLENDING = 'blending'
}

/**
 * 动画播放模式
 */
export enum PlayMode {
  /** 立即播放 */
  IMMEDIATE = 'immediate',
  /** 混合播放 */
  BLEND = 'blend',
  /** 排队播放 */
  QUEUE = 'queue',
  /** 叠加播放 */
  ADDITIVE = 'additive'
}

/**
 * 动画播放选项
 */
export interface AnimationPlayOptions {
  /** 播放模式 */
  mode?: PlayMode;
  /** 混合时间（秒） */
  blendTime?: number;
  /** 播放速度 */
  speed?: number;
  /** 权重 */
  weight?: number;
  /** 是否循环 */
  loop?: boolean;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 是否反向播放 */
  reverse?: boolean;
}

/**
 * 动画状态信息
 */
export interface AnimationStateInfo {
  /** 当前播放的动画片段名称 */
  currentClip: string | null;
  /** 动画状态 */
  state: AnimationState;
  /** 当前时间 */
  currentTime: number;
  /** 播放速度 */
  speed: number;
  /** 权重 */
  weight: number;
  /** 是否循环 */
  isLooping: boolean;
  /** 剩余时间 */
  remainingTime: number;
  /** 进度（0-1） */
  progress: number;
}

/**
 * 动画混合信息
 */
export interface BlendInfo {
  /** 源动画片段 */
  fromClip: string;
  /** 目标动画片段 */
  toClip: string;
  /** 混合进度（0-1） */
  blendProgress: number;
  /** 混合时间 */
  blendDuration: number;
}

/**
 * 动画队列项
 */
export interface AnimationQueueItem {
  /** 动画片段名称 */
  clipName: string;
  /** 播放选项 */
  options: AnimationPlayOptions;
  /** 优先级 */
  priority: number;
}

export class AnimationComponent extends Component {
  public static readonly type: string = 'AnimationComponent';

  /** 动画器 */
  private animator: Animator | null = null;

  /** 当前动画状态 */
  private animationState: AnimationState = AnimationState.STOPPED;

  /** 当前播放的动画片段名称 */
  private currentClip: string | null = null;

  /** 当前播放时间 */
  private currentTime: number = 0;

  /** 播放速度 */
  private playbackSpeed: number = 1.0;

  /** 动画权重 */
  private weight: number = 1.0;

  /** 是否循环播放 */
  private isLooping: boolean = false;

  /** 动画队列 */
  private animationQueue: AnimationQueueItem[] = [];

  /** 当前混合信息 */
  private currentBlend: BlendInfo | null = null;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 动画片段缓存 */
  private clipCache: Map<string, AnimationClip> = new Map();

  /** 自动播放的动画片段 */
  private autoPlayClip: string | null = null;

  /** 是否启用自动播放 */
  private autoPlayEnabled: boolean = false;

  /** 动画事件监听器 */
  private animationEventListeners: Map<string, Function[]> = new Map();

  constructor() {
    super(AnimationComponent.type);

    // 初始化性能统计
    this.performanceStats = {
      updateCount: 0,
      totalUpdateTime: 0,
      averageUpdateTime: 0,
      lastUpdateTime: 0,
      maxUpdateTime: 0,
      minUpdateTime: Number.MAX_VALUE
    };
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new AnimationComponent();
  }

  /**
   * 设置动画器
   * @param animator 动画器实例
   */
  public setAnimator(animator: Animator): void {
    this.animator = animator;

    // 设置动画事件监听
    if (this.animator && typeof this.animator.on === 'function') {
      this.animator.on('animationStart', this.onAnimationStart.bind(this));
      this.animator.on('animationEnd', this.onAnimationEnd.bind(this));
      this.animator.on('animationLoop', this.onAnimationLoop.bind(this));
    }
  }

  /**
   * 获取动画器
   */
  public getAnimator(): Animator | null {
    return this.animator;
  }

  /**
   * 播放动画
   * @param clipName 动画片段名称
   * @param options 播放选项
   */
  public play(clipName: string, options: AnimationPlayOptions = {}): void {
    if (!this.animator) {
      console.warn('AnimationComponent: 没有设置动画器');
      return;
    }

    const playOptions = {
      mode: PlayMode.IMMEDIATE,
      blendTime: 0.3,
      speed: 1.0,
      weight: 1.0,
      loop: false,
      startTime: 0,
      reverse: false,
      ...options
    };

    // 根据播放模式处理
    switch (playOptions.mode) {
      case PlayMode.IMMEDIATE:
        this.playImmediate(clipName, playOptions);
        break;
      case PlayMode.BLEND:
        this.playWithBlend(clipName, playOptions);
        break;
      case PlayMode.QUEUE:
        this.addToQueue(clipName, playOptions);
        break;
      case PlayMode.ADDITIVE:
        this.playAdditive(clipName, playOptions);
        break;
    }
  }

  /**
   * 停止动画
   */
  public stop(): void {
    if (this.animator) {
      this.animator.stop();
      this.animationState = AnimationState.STOPPED;
      this.currentClip = null;
      this.currentTime = 0;
      this.currentBlend = null;
      this.animationQueue = [];

      this.eventEmitter.emit('animationStopped');
    }
  }

  /**
   * 暂停动画
   */
  public pause(): void {
    if (this.animator && this.animationState === AnimationState.PLAYING) {
      this.animator.pause();
      this.animationState = AnimationState.PAUSED;

      this.eventEmitter.emit('animationPaused');
    }
  }

  /**
   * 恢复动画
   */
  public resume(): void {
    if (this.animator && this.animationState === AnimationState.PAUSED) {
      this.animator.resume();
      this.animationState = AnimationState.PLAYING;

      this.eventEmitter.emit('animationResumed');
    }
  }

  /**
   * 更新动画
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    const startTime = performance.now();

    if (this.animator && this.animationState === AnimationState.PLAYING) {
      // 更新当前时间
      this.currentTime += deltaTime * this.playbackSpeed;

      // 更新动画器
      this.animator.update(deltaTime);

      // 处理混合
      this.updateBlending(deltaTime);

      // 处理队列
      this.updateQueue();

      // 检查动画完成
      this.checkAnimationCompletion();
    }

    // 更新性能统计
    this.updateAnimationPerformanceStats(performance.now() - startTime);
  }

  /**
   * 获取是否正在播放
   */
  public getIsPlaying(): boolean {
    return this.animationState === AnimationState.PLAYING;
  }

  /**
   * 获取当前动画片段名称
   */
  public getCurrentClip(): string | null {
    return this.currentClip;
  }

  /**
   * 获取动画状态信息
   */
  public getStateInfo(): AnimationStateInfo {
    const clip = this.currentClip ? this.getClip(this.currentClip) : null;
    const duration = clip ? clip.duration : 0;

    return {
      currentClip: this.currentClip,
      state: this.animationState,
      currentTime: this.currentTime,
      speed: this.playbackSpeed,
      weight: this.weight,
      isLooping: this.isLooping,
      remainingTime: duration > 0 ? Math.max(0, duration - this.currentTime) : 0,
      progress: duration > 0 ? Math.min(1, this.currentTime / duration) : 0
    };
  }

  /**
   * 设置播放速度
   * @param speed 播放速度
   */
  public setSpeed(speed: number): void {
    this.playbackSpeed = Math.max(0, speed);
    // 注意：Animator 类可能不支持 setSpeed 方法
    // 如果需要，可以通过其他方式实现速度控制
  }

  /**
   * 获取播放速度
   */
  public getSpeed(): number {
    return this.playbackSpeed;
  }

  /**
   * 设置权重
   * @param weight 权重值
   */
  public setWeight(weight: number): void {
    this.weight = Math.max(0, Math.min(1, weight));
    // 注意：Animator 类可能不支持 setWeight 方法
    // 如果需要，可以通过其他方式实现权重控制
  }

  /**
   * 获取权重
   */
  public getWeight(): number {
    return this.weight;
  }

  /**
   * 设置循环模式
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    this.isLooping = loop;
    if (this.animator && typeof this.animator.setLoop === 'function') {
      this.animator.setLoop(loop);
    }
  }

  /**
   * 获取是否循环
   */
  public getLoop(): boolean {
    return this.isLooping;
  }

  /**
   * 添加动画片段
   * @param name 动画片段名称
   * @param clip 动画片段
   */
  public addClip(name: string, clip: AnimationClip): void {
    this.clipCache.set(name, clip);

    // 设置动画事件监听
    clip.on(AnimationEventType.START, (data) => {
      this.eventEmitter.emit('clipStart', { clipName: name, ...data });
    });

    clip.on(AnimationEventType.END, (data) => {
      this.eventEmitter.emit('clipEnd', { clipName: name, ...data });
    });

    clip.on(AnimationEventType.LOOP, (data) => {
      this.eventEmitter.emit('clipLoop', { clipName: name, ...data });
    });
  }

  /**
   * 移除动画片段
   * @param name 动画片段名称
   */
  public removeClip(name: string): boolean {
    if (this.currentClip === name) {
      this.stop();
    }
    return this.clipCache.delete(name);
  }

  /**
   * 获取动画片段
   * @param name 动画片段名称
   */
  public getClip(name: string): AnimationClip | null {
    return this.clipCache.get(name) || null;
  }

  /**
   * 获取所有动画片段名称
   */
  public getClipNames(): string[] {
    return Array.from(this.clipCache.keys());
  }

  /**
   * 设置自动播放
   * @param clipName 动画片段名称
   * @param enabled 是否启用
   */
  public setAutoPlay(clipName: string, enabled: boolean = true): void {
    this.autoPlayClip = enabled ? clipName : null;
    this.autoPlayEnabled = enabled;
  }

  /**
   * 添加动画事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: EventCallback): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除动画事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback?: EventCallback): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats() {
    return { ...this.performanceStats };
  }

  /**
   * 重置性能统计
   */
  public resetPerformanceStats(): void {
    this.performanceStats = {
      updateCount: 0,
      totalUpdateTime: 0,
      averageUpdateTime: 0,
      lastUpdateTime: 0,
      maxUpdateTime: 0,
      minUpdateTime: Number.MAX_VALUE
    };
  }

  public dispose(): void {
    // 停止所有动画
    this.stop();

    // 清理事件监听器
    this.eventEmitter.removeAllListeners();
    this.animationEventListeners.clear();

    // 清理缓存
    this.clipCache.clear();

    // 清理动画器
    if (this.animator) {
      if (typeof this.animator.dispose === 'function') {
        this.animator.dispose();
      }
      this.animator = null;
    }

    super.dispose();
  }

  // ==================== 私有方法 ====================

  /**
   * 立即播放动画
   */
  private playImmediate(clipName: string, options: AnimationPlayOptions): void {
    if (this.animator) {
      this.currentClip = clipName;
      this.currentTime = options.startTime || 0;
      this.playbackSpeed = options.speed || 1.0;
      this.weight = options.weight || 1.0;
      this.isLooping = options.loop || false;
      this.animationState = AnimationState.PLAYING;

      this.animator.play(clipName);
      this.eventEmitter.emit('animationStarted', { clipName, options });
    }
  }

  /**
   * 混合播放动画
   */
  private playWithBlend(clipName: string, options: AnimationPlayOptions): void {
    if (this.currentClip && this.currentClip !== clipName) {
      this.currentBlend = {
        fromClip: this.currentClip,
        toClip: clipName,
        blendProgress: 0,
        blendDuration: options.blendTime || 0.3
      };
      this.animationState = AnimationState.BLENDING;
    }

    this.playImmediate(clipName, options);
  }

  /**
   * 添加到播放队列
   */
  private addToQueue(clipName: string, options: AnimationPlayOptions): void {
    this.animationQueue.push({
      clipName,
      options,
      priority: options.weight || 1.0
    });

    // 按优先级排序
    this.animationQueue.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 叠加播放动画
   */
  private playAdditive(clipName: string, options: AnimationPlayOptions): void {
    // 叠加播放的实现需要动画器支持
    // 注意：当前 Animator 类不支持 playAdditive 方法
    // 回退到混合播放
    this.playWithBlend(clipName, options);
  }

  /**
   * 更新混合状态
   */
  private updateBlending(deltaTime: number): void {
    if (this.currentBlend) {
      this.currentBlend.blendProgress += deltaTime / this.currentBlend.blendDuration;

      if (this.currentBlend.blendProgress >= 1.0) {
        this.currentBlend = null;
        this.animationState = AnimationState.PLAYING;
        this.eventEmitter.emit('blendCompleted');
      }
    }
  }

  /**
   * 更新播放队列
   */
  private updateQueue(): void {
    if (this.animationQueue.length > 0 && this.animationState === AnimationState.STOPPED) {
      const nextItem = this.animationQueue.shift()!;
      this.play(nextItem.clipName, nextItem.options);
    }
  }

  /**
   * 检查动画完成
   */
  private checkAnimationCompletion(): void {
    const clip = this.currentClip ? this.getClip(this.currentClip) : null;
    if (clip && this.currentTime >= clip.duration) {
      if (this.isLooping) {
        this.currentTime = 0;
        this.eventEmitter.emit('animationLoop', { clipName: this.currentClip });
      } else {
        this.animationState = AnimationState.STOPPED;
        this.eventEmitter.emit('animationCompleted', { clipName: this.currentClip });

        // 播放队列中的下一个动画
        this.updateQueue();
      }
    }
  }

  /**
   * 动画开始事件处理
   */
  private onAnimationStart(data: any): void {
    this.eventEmitter.emit('animationStart', data);
  }

  /**
   * 动画结束事件处理
   */
  private onAnimationEnd(data: any): void {
    this.eventEmitter.emit('animationEnd', data);
  }

  /**
   * 动画循环事件处理
   */
  private onAnimationLoop(data: any): void {
    this.eventEmitter.emit('animationLoop', data);
  }

  /**
   * 更新性能统计
   */
  private updateAnimationPerformanceStats(updateTime: number): void {
    this.performanceStats.updateCount++;
    this.performanceStats.totalUpdateTime += updateTime;
    this.performanceStats.lastUpdateTime = updateTime;
    this.performanceStats.averageUpdateTime = this.performanceStats.totalUpdateTime / this.performanceStats.updateCount;

    if (updateTime > this.performanceStats.maxUpdateTime) {
      this.performanceStats.maxUpdateTime = updateTime;
    }

    if (updateTime < this.performanceStats.minUpdateTime) {
      this.performanceStats.minUpdateTime = updateTime;
    }
  }
}
