/**
 * IES光源
 * 基于IES文件的光源，用于模拟真实世界的灯具光照分布
 */
import * as THREE from 'three';
// 使用类型断言导入 IESLoader
// @ts-ignore
import { IESLoader } from 'three/examples/jsm/loaders/IESLoader.js';
import { Component } from '../../core/Component';

/**
 * IES光源类型枚举
 */
export enum IESLightType {
  POINT = 'point',
  SPOT = 'spot'
}

/**
 * IES光源选项接口
 */
export interface IESLightOptions {
  /** 光源类型 */
  type?: IESLightType;
  /** 光源颜色 */
  color?: THREE.ColorRepresentation;
  /** 光源强度 */
  intensity?: number;
  /** 是否投射阴影 */
  castShadow?: boolean;
  /** 光源距离 */
  distance?: number;
  /** 光源衰减 */
  decay?: number;
  /** IES文件路径 */
  iesFilePath?: string;
  /** IES文件数据 */
  iesData?: string;
  /** IES纹理 */
  iesTexture?: THREE.Texture;
  /** 是否使用IES纹理 */
  useIESTexture?: boolean;
  /** 阴影贴图大小 */
  shadowMapSize?: number;
  /** 阴影相机近平面 */
  shadowCameraNear?: number;
  /** 阴影相机远平面 */
  shadowCameraFar?: number;
  /** 阴影偏移 */
  shadowBias?: number;
  /** 阴影半径 */
  shadowRadius?: number;
}

/**
 * IES光源组件类
 */
export class IESLight extends Component {
  /** 组件类型 */
  public static readonly type: string = 'IESLight';

  /** 光源类型 */
  private lightType: IESLightType;

  /** Three.js光源 */
  private light: THREE.Light;

  /** IES加载器 */
  private iesLoader: IESLoader;

  /** IES纹理 */
  private iesTexture: THREE.Texture | null = null;

  /** 是否使用IES纹理 */
  private useIESTexture: boolean;

  /** 是否已加载IES纹理 */
  private iesTextureLoaded: boolean = false;

  /** 加载完成回调 */
  private onLoadCallbacks: Array<() => void> = [];

  /**
   * 创建IES光源组件
   * @param options IES光源选项
   */
  constructor(options: IESLightOptions = {}) {
    super(IESLight.type);

    this.lightType = options.type || IESLightType.SPOT;
    this.useIESTexture = options.useIESTexture !== undefined ? options.useIESTexture : true;

    // 创建IES加载器
    this.iesLoader = new IESLoader();

    // 创建对应类型的光源
    switch (this.lightType) {
      case IESLightType.POINT:
        this.light = this.createPointLight(options);
        break;
      case IESLightType.SPOT:
        this.light = this.createSpotLight(options);
        break;
      default:
        throw new Error(`不支持的IES光源类型: ${this.lightType}`);
    }

    // 加载IES纹理
    if (this.useIESTexture) {
      if (options.iesTexture) {
        this.iesTexture = options.iesTexture;
        this.iesTextureLoaded = true;
        this.applyIESTexture();
      } else if (options.iesFilePath) {
        this.loadIESFile(options.iesFilePath);
      } else if (options.iesData) {
        this.parseIESData(options.iesData);
      }
    }
  }

  /**
   * 创建点光源
   * @param options IES光源选项
   * @returns Three.js点光源
   */
  private createPointLight(options: IESLightOptions): THREE.PointLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const distance = options.distance !== undefined ? options.distance : 0;
    const decay = options.decay !== undefined ? options.decay : 2;
    
    const light = new THREE.PointLight(color, intensity, distance, decay);

    // 设置阴影
    light.castShadow = options.castShadow !== undefined ? options.castShadow : false;
    
    if (light.castShadow) {
      // 设置阴影贴图大小
      if (options.shadowMapSize !== undefined) {
        light.shadow.mapSize.width = options.shadowMapSize;
        light.shadow.mapSize.height = options.shadowMapSize;
      }
      
      // 设置阴影相机
      if (options.shadowCameraNear !== undefined) {
        light.shadow.camera.near = options.shadowCameraNear;
      }
      
      if (options.shadowCameraFar !== undefined) {
        light.shadow.camera.far = options.shadowCameraFar;
      }
      
      // 设置阴影偏移
      if (options.shadowBias !== undefined) {
        light.shadow.bias = options.shadowBias;
      }
      
      // 设置阴影半径
      if (options.shadowRadius !== undefined) {
        light.shadow.radius = options.shadowRadius;
      }
    }
    
    return light;
  }

  /**
   * 创建聚光灯
   * @param options IES光源选项
   * @returns Three.js聚光灯
   */
  private createSpotLight(options: IESLightOptions): THREE.SpotLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const distance = options.distance !== undefined ? options.distance : 0;
    const decay = options.decay !== undefined ? options.decay : 2;
    
    // 创建聚光灯，使用较大的角度以便IES纹理控制光照分布
    const light = new THREE.SpotLight(color, intensity, distance, Math.PI / 2, 1, decay);

    // 设置阴影
    light.castShadow = options.castShadow !== undefined ? options.castShadow : false;
    
    if (light.castShadow) {
      // 设置阴影贴图大小
      if (options.shadowMapSize !== undefined) {
        light.shadow.mapSize.width = options.shadowMapSize;
        light.shadow.mapSize.height = options.shadowMapSize;
      }
      
      // 设置阴影相机
      if (options.shadowCameraNear !== undefined) {
        light.shadow.camera.near = options.shadowCameraNear;
      }
      
      if (options.shadowCameraFar !== undefined) {
        light.shadow.camera.far = options.shadowCameraFar;
      }
      
      // 设置阴影偏移
      if (options.shadowBias !== undefined) {
        light.shadow.bias = options.shadowBias;
      }
      
      // 设置阴影半径
      if (options.shadowRadius !== undefined) {
        light.shadow.radius = options.shadowRadius;
      }
    }
    
    return light;
  }

  /**
   * 加载IES文件
   * @param filePath IES文件路径
   */
  public loadIESFile(filePath: string): void {
    this.iesLoader.load(filePath, (iesTexture: THREE.Texture) => {
      this.iesTexture = iesTexture;
      this.iesTextureLoaded = true;
      this.applyIESTexture();
      
      // 调用加载完成回调
      this.onLoadCallbacks.forEach(callback => callback());
      this.onLoadCallbacks = [];
    });
  }

  /**
   * 解析IES数据
   * @param iesData IES文件数据
   */
  public parseIESData(iesData: string): void {
    const iesTexture = this.iesLoader.parse(iesData);
    this.iesTexture = iesTexture;
    this.iesTextureLoaded = true;
    this.applyIESTexture();
    
    // 调用加载完成回调
    this.onLoadCallbacks.forEach(callback => callback());
    this.onLoadCallbacks = [];
  }

  /**
   * 应用IES纹理
   */
  private applyIESTexture(): void {
    if (!this.iesTexture || !this.useIESTexture) return;

    // 根据光源类型应用IES纹理
    if (this.light instanceof THREE.SpotLight) {
      this.light.map = this.iesTexture;
    } else if (this.light instanceof THREE.PointLight) {
      // 点光源不直接支持map属性，需要使用自定义着色器
      // 这里简化处理，实际应用中可能需要更复杂的实现
      console.warn('点光源不直接支持IES纹理，需要使用自定义着色器');
    }
  }

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getTransform();
    if (transform) {
      // 将光源添加到变换的Three.js对象
      transform.getObject3D().add(this.light);

      // 如果是聚光灯，还需要添加目标
      if (this.light instanceof THREE.SpotLight) {
        transform.getObject3D().add(this.light.target);
      }
    }
  }

  /**
   * 当组件从实体分离时调用
   */
  protected onDetach(): void {
    // 清理资源
    if (this.iesTexture) {
      (this.iesTexture as any).dispose();
      this.iesTexture = null;
    }
  }

  /**
   * 获取光源类型
   * @returns 光源类型
   */
  public getType(): IESLightType {
    return this.lightType;
  }

  /**
   * 获取Three.js光源
   * @returns Three.js光源
   */
  public getThreeLight(): THREE.Light {
    return this.light;
  }

  /**
   * 获取IES纹理
   * @returns IES纹理
   */
  public getIESTexture(): THREE.Texture | null {
    return this.iesTexture;
  }

  /**
   * 设置IES纹理
   * @param texture IES纹理
   */
  public setIESTexture(texture: THREE.Texture): void {
    if (this.iesTexture) {
      (this.iesTexture as any).dispose();
    }
    
    this.iesTexture = texture;
    this.iesTextureLoaded = true;
    this.applyIESTexture();
  }

  /**
   * 设置是否使用IES纹理
   * @param use 是否使用
   */
  public setUseIESTexture(use: boolean): void {
    this.useIESTexture = use;
    
    if (use && this.iesTexture) {
      this.applyIESTexture();
    } else if (!use && this.light instanceof THREE.SpotLight) {
      this.light.map = null;
    }
  }

  /**
   * 设置光源颜色
   * @param color 颜色
   */
  public setColor(color: THREE.ColorRepresentation): void {
    if (this.light instanceof THREE.Light) {
      this.light.color.set(color);
    }
  }

  /**
   * 设置光源强度
   * @param intensity 强度
   */
  public setIntensity(intensity: number): void {
    if (this.light instanceof THREE.Light) {
      this.light.intensity = intensity;
    }
  }

  /**
   * 设置是否投射阴影
   * @param castShadow 是否投射阴影
   */
  public setCastShadow(castShadow: boolean): void {
    if (this.light instanceof THREE.PointLight || this.light instanceof THREE.SpotLight) {
      this.light.castShadow = castShadow;
    }
  }

  /**
   * 设置光源距离
   * @param distance 距离
   */
  public setDistance(distance: number): void {
    if (this.light instanceof THREE.PointLight || this.light instanceof THREE.SpotLight) {
      this.light.distance = distance;
    }
  }

  /**
   * 设置光源衰减
   * @param decay 衰减
   */
  public setDecay(decay: number): void {
    if (this.light instanceof THREE.PointLight || this.light instanceof THREE.SpotLight) {
      this.light.decay = decay;
    }
  }

  /**
   * 当IES纹理加载完成时调用回调
   * @param callback 回调函数
   */
  public onLoad(callback: () => void): void {
    if (this.iesTextureLoaded) {
      callback();
    } else {
      this.onLoadCallbacks.push(callback);
    }
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    // 获取当前光源的属性
    let color = 0xffffff;
    let intensity = 1;
    let distance = 0;
    let decay = 2;
    let castShadow = false;

    if (this.light instanceof THREE.PointLight || this.light instanceof THREE.SpotLight) {
      color = this.light.color.getHex();
      intensity = this.light.intensity;
      distance = this.light.distance;
      decay = this.light.decay;
      castShadow = this.light.castShadow;
    }

    return new IESLight({
      type: this.lightType,
      color: color,
      intensity: intensity,
      distance: distance,
      decay: decay,
      castShadow: castShadow,
      iesTexture: this.iesTexture,
      useIESTexture: this.useIESTexture
    });
  }
}
