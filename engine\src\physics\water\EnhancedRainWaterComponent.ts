/**
 * 增强版雨水组件
 * 提供更真实的雨水物理模拟和渲染效果
 */
import * as THREE from 'three';
import { WaterBodyType } from './WaterBodyComponent';
import { RainWaterComponent, RainWaterConfig, RainWaterType } from './RainWaterComponent';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { Component } from '../../core/Component';
import { AudioSystem } from '../../audio/AudioSystem';
import { UnderwaterParticleSystem } from '../../rendering/water/UnderwaterParticleSystem';
import { WaterInstancedRenderer, WaterEffectType } from '../../rendering/water/WaterInstancedRenderer';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 增强版雨水类型
 */
export enum EnhancedRainWaterType {
  /** 轻雨 */
  LIGHT = 'light',
  /** 中雨 */
  MEDIUM = 'medium',
  /** 暴雨 */
  HEAVY = 'heavy',
  /** 雷雨 */
  THUNDERSTORM = 'thunderstorm',
  /** 季风雨 */
  MONSOON = 'monsoon',
  /** 春雨 */
  SPRING_RAIN = 'spring_rain',
  /** 夏雨 */
  SUMMER_RAIN = 'summer_rain',
  /** 秋雨 */
  AUTUMN_RAIN = 'autumn_rain',
  /** 冬雨 */
  WINTER_RAIN = 'winter_rain',
  /** 雪雨混合 */
  SLEET = 'sleet',
  /** 酸雨 */
  ACID_RAIN = 'acid_rain',
  /** 热带雨 */
  TROPICAL_RAIN = 'tropical_rain'
}

/**
 * 雨滴形状
 */
export enum RaindropShape {
  /** 球形 */
  SPHERE = 'sphere',
  /** 泪滴形 */
  TEARDROP = 'teardrop',
  /** 扁平 */
  FLAT = 'flat',
  /** 不规则 */
  IRREGULAR = 'irregular'
}

/**
 * 增强版雨水配置
 */
export interface EnhancedRainWaterConfig extends RainWaterConfig {
  /** 雨滴形状 */
  raindropShape?: RaindropShape;
  /** 雨滴变形系数 */
  raindropDeformationFactor?: number;
  /** 雨滴碰撞精度 */
  raindropCollisionPrecision?: number;
  /** 是否启用风力影响 */
  enableWindEffect?: boolean;
  /** 风力强度 */
  windStrength?: number;
  /** 风力方向 */
  windDirection?: THREE.Vector2;
  /** 是否启用雨滴轨迹 */
  enableRaindropTrails?: boolean;
  /** 雨滴轨迹长度 */
  raindropTrailLength?: number;
  /** 是否启用雨滴涟漪 */
  enableRaindropRipples?: boolean;
  /** 雨滴涟漪强度 */
  raindropRippleStrength?: number;
  /** 是否启用雨滴积水 */
  enableRaindropPuddles?: boolean;
  /** 雨滴积水最大深度 */
  raindropPuddleMaxDepth?: number;
  /** 是否启用雨滴反射 */
  enableRaindropReflections?: boolean;
  /** 雨滴反射强度 */
  raindropReflectionStrength?: number;
  /** 是否启用雨滴折射 */
  enableRaindropRefractions?: boolean;
  /** 雨滴折射强度 */
  raindropRefractionStrength?: number;
  /** 是否启用雨滴光散射 */
  enableRaindropScattering?: boolean;
  /** 雨滴光散射强度 */
  raindropScatteringStrength?: number;
  /** 是否启用雨滴声音 */
  enableRaindropSounds?: boolean;
  /** 雨滴声音音量 */
  raindropSoundVolume?: number;
  /** 是否启用雨滴性能优化 */
  enableRaindropOptimization?: boolean;
  /** 雨滴LOD距离 */
  raindropLODDistances?: number[];
  /** 是否启用GPU加速 */
  enableGPUAcceleration?: boolean;
}

/**
 * 雨滴碰撞检测器
 */
class RaindropCollisionDetector {
  /** 世界 */
  private world: World;
  /** 精度 */
  private precision: number;
  /** 射线投射器 */
  private raycaster: THREE.Raycaster;
  /** 临时向量 */
  private tempVector: THREE.Vector3;
  /** 临时方向 */
  private tempDirection: THREE.Vector3;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor;

  /**
   * 构造函数
   * @param world 世界
   * @param precision 精度
   */
  constructor(world: World, precision: number = 1.0) {
    this.world = world;
    this.precision = precision;
    this.raycaster = new THREE.Raycaster();
    this.tempVector = new THREE.Vector3();
    this.tempDirection = new THREE.Vector3(0, -1, 0);
    this.performanceMonitor = PerformanceMonitor.getInstance();
  }

  /**
   * 检测碰撞
   * @param position 位置
   * @param direction 方向
   * @param maxDistance 最大距离
   * @returns 碰撞结果
   */
  public detectCollision(position: THREE.Vector3, direction: THREE.Vector3, maxDistance: number): THREE.Intersection | null {
    // 开始性能监控
    this.performanceMonitor.beginMeasure('raindropCollisionDetection');

    // 设置射线投射器
    this.raycaster.set(position, direction.normalize());
    this.raycaster.far = maxDistance;

    // 获取场景中的物体
    const scene = this.world.getActiveScene();
    const objects = scene ? (scene as any).children.filter((child: any) => {
      return child.type === 'Mesh' && child.visible &&
             (child as THREE.Mesh).geometry !== undefined &&
             (child as THREE.Mesh).material !== undefined;
    }) : [];

    // 执行射线投射
    const intersects = this.raycaster.intersectObjects(objects, true);

    // 结束性能监控
    this.performanceMonitor.endMeasure('raindropCollisionDetection');

    // 返回最近的碰撞结果
    return intersects.length > 0 ? intersects[0] : null;
  }

  /**
   * 设置精度
   * @param precision 精度
   */
  public setPrecision(precision: number): void {
    this.precision = precision;
  }

  /**
   * 获取性能监控器
   * @returns 性能监控器
   */
  public getPerformanceMonitor(): PerformanceMonitor {
    return this.performanceMonitor;
  }
}

/**
 * 增强版雨水组件
 */
export class EnhancedRainWaterComponent extends RainWaterComponent {
  /** 雨滴形状 */
  private raindropShape: RaindropShape = RaindropShape.TEARDROP;
  /** 雨滴变形系数 */
  private raindropDeformationFactor: number = 1.0;
  /** 雨滴碰撞精度 */
  private raindropCollisionPrecision: number = 1.0;
  /** 是否启用风力影响 */
  private enableWindEffect: boolean = true;
  /** 风力强度 */
  private windStrength: number = 1.0;
  /** 风力方向 */
  private windDirection: THREE.Vector2 = new THREE.Vector2(1, 0);
  /** 是否启用雨滴轨迹 */
  private enableRaindropTrails: boolean = true;
  /** 雨滴轨迹长度 */
  private raindropTrailLength: number = 1.0;
  /** 是否启用雨滴涟漪 */
  private enableRaindropRipples: boolean = true;
  /** 雨滴涟漪强度 */
  private raindropRippleStrength: number = 1.0;
  /** 是否启用雨滴积水 */
  private enableRaindropPuddles: boolean = true;
  /** 雨滴积水最大深度 */
  private raindropPuddleMaxDepth: number = 0.1;
  /** 是否启用雨滴反射 */
  private enableRaindropReflections: boolean = true;
  /** 雨滴反射强度 */
  private raindropReflectionStrength: number = 1.0;
  /** 是否启用雨滴折射 */
  private enableRaindropRefractions: boolean = true;
  /** 雨滴折射强度 */
  private raindropRefractionStrength: number = 1.0;
  /** 是否启用雨滴光散射 */
  private enableRaindropScattering: boolean = true;
  /** 雨滴光散射强度 */
  private raindropScatteringStrength: number = 1.0;
  /** 是否启用雨滴声音 */
  private enableRaindropSounds: boolean = true;
  /** 雨滴声音音量 */
  private raindropSoundVolume: number = 1.0;
  /** 是否启用雨滴性能优化 */
  private enableRaindropOptimization: boolean = true;
  /** 雨滴LOD距离 */
  private raindropLODDistances: number[] = [10, 20, 50, 100];
  /** 是否启用GPU加速 */
  private enableGPUAcceleration: boolean = true;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 雨滴实例ID列表 */
  private raindropInstanceIds: string[] = [];
  /** 雨滴积水网格 */
  private puddleMeshes: THREE.Mesh[] = [];
  /** 雨滴轨迹实例ID列表 */
  private raindropTrailInstanceIds: string[] = [];
  /** 雨滴涟漪实例ID列表 */
  private raindropRippleInstanceIds: string[] = [];
  /** 雨滴碰撞检测器 */
  private raindropCollisionDetector: RaindropCollisionDetector | null = null;
  /** 雨滴生成计时器 */
  private raindropGenerationTimer: number = 0;
  /** 雨滴生成间隔 */
  private raindropGenerationInterval: number = 0.1;
  /** 雨滴生成区域 */
  private raindropGenerationArea: THREE.Box3 = new THREE.Box3();
  /** 雨滴生成高度 */
  private raindropGenerationHeight: number = 10.0;
  /** 雨滴最大数量 */
  private maxRaindrops: number = 1000;
  /** 当前雨滴数量 */
  private currentRaindropCount: number = 0;
  /** 水体实例化渲染器 */
  private waterInstancedRenderer: WaterInstancedRenderer | null = null;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem | null = null;
  /** 音频系统 */
  private audioSystem: AudioSystem | null = null;
  /** 音频源ID */
  private audioSourceId: string = '';

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: EnhancedRainWaterConfig = {}) {
    // 确保水体类型设置为雨水
    const enhancedConfig = {
      ...config,
      type: WaterBodyType.RAIN_WATER
    };

    super(entity, enhancedConfig);

    // 应用增强配置
    this.applyEnhancedConfig(config);
  }

  /**
   * 应用增强配置
   * @param config 配置
   */
  private applyEnhancedConfig(config: EnhancedRainWaterConfig): void {
    // 应用基本配置
    if (config.raindropShape !== undefined) this.raindropShape = config.raindropShape;
    if (config.raindropDeformationFactor !== undefined) this.raindropDeformationFactor = config.raindropDeformationFactor;
    if (config.raindropCollisionPrecision !== undefined) this.raindropCollisionPrecision = config.raindropCollisionPrecision;
    if (config.enableWindEffect !== undefined) this.enableWindEffect = config.enableWindEffect;
    if (config.windStrength !== undefined) this.windStrength = config.windStrength;
    if (config.windDirection !== undefined) this.windDirection = config.windDirection;
    if (config.enableRaindropTrails !== undefined) this.enableRaindropTrails = config.enableRaindropTrails;
    if (config.raindropTrailLength !== undefined) this.raindropTrailLength = config.raindropTrailLength;
    if (config.enableRaindropRipples !== undefined) this.enableRaindropRipples = config.enableRaindropRipples;
    if (config.raindropRippleStrength !== undefined) this.raindropRippleStrength = config.raindropRippleStrength;
    if (config.enableRaindropPuddles !== undefined) this.enableRaindropPuddles = config.enableRaindropPuddles;
    if (config.raindropPuddleMaxDepth !== undefined) this.raindropPuddleMaxDepth = config.raindropPuddleMaxDepth;
    if (config.enableRaindropReflections !== undefined) this.enableRaindropReflections = config.enableRaindropReflections;
    if (config.raindropReflectionStrength !== undefined) this.raindropReflectionStrength = config.raindropReflectionStrength;
    if (config.enableRaindropRefractions !== undefined) this.enableRaindropRefractions = config.enableRaindropRefractions;
    if (config.raindropRefractionStrength !== undefined) this.raindropRefractionStrength = config.raindropRefractionStrength;
    if (config.enableRaindropScattering !== undefined) this.enableRaindropScattering = config.enableRaindropScattering;
    if (config.raindropScatteringStrength !== undefined) this.raindropScatteringStrength = config.raindropScatteringStrength;
    if (config.enableRaindropSounds !== undefined) this.enableRaindropSounds = config.enableRaindropSounds;
    if (config.raindropSoundVolume !== undefined) this.raindropSoundVolume = config.raindropSoundVolume;
    if (config.enableRaindropOptimization !== undefined) this.enableRaindropOptimization = config.enableRaindropOptimization;
    if (config.raindropLODDistances !== undefined) this.raindropLODDistances = config.raindropLODDistances;
    if (config.enableGPUAcceleration !== undefined) this.enableGPUAcceleration = config.enableGPUAcceleration;
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    // 调用父类初始化
    super.initialize();

    // 获取世界
    const world = this.entity.getWorld();

    // 初始化碰撞检测器
    this.raindropCollisionDetector = new RaindropCollisionDetector(world, this.raindropCollisionPrecision);

    // 获取水体实例化渲染器
    this.waterInstancedRenderer = world.getSystem(WaterInstancedRenderer) as WaterInstancedRenderer;

    // 获取水下粒子系统
    try {
      this.underwaterParticleSystem = world.getSystem(UnderwaterParticleSystem as any) as unknown as UnderwaterParticleSystem;
    } catch (error) {
      Debug.warn('EnhancedRainWaterComponent', '无法获取水下粒子系统:', error);
      this.underwaterParticleSystem = null;
    }

    // 获取音频系统
    this.audioSystem = world.getSystem(AudioSystem) as AudioSystem;

    // 初始化雨滴生成区域
    this.initializeRaindropGenerationArea();

    // 初始化雨滴生成间隔
    this.updateRaindropGenerationInterval();

    // 初始化音频
    this.initializeEnhancedAudio();

    // 初始化积水
    if (this.enableRaindropPuddles) {
      this.initializePuddles();
    }

    Debug.log('EnhancedRainWaterComponent', '增强版雨水组件初始化完成');
  }

  /**
   * 初始化雨滴生成区域
   */
  private initializeRaindropGenerationArea(): void {
    // 获取水体位置和尺寸
    const position = this.getPosition();
    const size = this.getSize();

    // 设置雨滴生成区域
    this.raindropGenerationArea.min.set(
      position.x - size.width / 2,
      position.y,
      position.z - size.depth / 2
    );
    this.raindropGenerationArea.max.set(
      position.x + size.width / 2,
      position.y + this.raindropGenerationHeight,
      position.z + size.depth / 2
    );
  }

  /**
   * 更新雨滴生成间隔
   */
  private updateRaindropGenerationInterval(): void {
    // 根据雨水类型设置生成间隔
    const rainType = this.getRainWaterType();
    switch (rainType) {
      case RainWaterType.LIGHT:
        this.raindropGenerationInterval = 0.2;
        this.maxRaindrops = 500;
        break;
      case RainWaterType.MEDIUM:
        this.raindropGenerationInterval = 0.1;
        this.maxRaindrops = 1000;
        break;
      case RainWaterType.HEAVY:
        this.raindropGenerationInterval = 0.05;
        this.maxRaindrops = 2000;
        break;
      case RainWaterType.THUNDERSTORM:
        this.raindropGenerationInterval = 0.03;
        this.maxRaindrops = 3000;
        break;
      case RainWaterType.MONSOON:
        this.raindropGenerationInterval = 0.02;
        this.maxRaindrops = 4000;
        break;
      default:
        this.raindropGenerationInterval = 0.1;
        this.maxRaindrops = 1000;
        break;
    }
  }

  /**
   * 初始化增强音频
   */
  private initializeEnhancedAudio(): void {
    // 如果未启用雨滴声音，则返回
    if (!this.enableRaindropSounds || !this.audioSystem) {
      return;
    }

    // 创建音频源
    const audioSource = this.audioSystem.createSource(this.entity.id);
    if (audioSource) {
      this.audioSourceId = this.entity.id;
      // 设置音频源属性
      audioSource.setVolume(this.raindropSoundVolume);
      audioSource.setLoop(true);
      audioSource.setSpatial(true);
      const pos = this.getPosition();
      audioSource.setPosition(pos.x, pos.y, pos.z);
      audioSource.setRefDistance(5.0);
      audioSource.setMaxDistance(50.0);
    }

    // 根据雨水类型选择音频
    let audioFile = 'sounds/rain_medium.mp3';
    const rainType = this.getRainWaterType();
    switch (rainType) {
      case RainWaterType.LIGHT:
        audioFile = 'sounds/rain_light.mp3';
        break;
      case RainWaterType.MEDIUM:
        audioFile = 'sounds/rain_medium.mp3';
        break;
      case RainWaterType.HEAVY:
        audioFile = 'sounds/rain_heavy.mp3';
        break;
      case RainWaterType.THUNDERSTORM:
        audioFile = 'sounds/rain_thunderstorm.mp3';
        break;
      case RainWaterType.MONSOON:
        audioFile = 'sounds/rain_monsoon.mp3';
        break;
      default:
        audioFile = 'sounds/rain_medium.mp3';
        break;
    }

    // 播放雨声
    this.audioSystem.play(this.audioSourceId, audioFile, {
      loop: true,
      volume: this.raindropSoundVolume
    });

    Debug.log('EnhancedRainWaterComponent', '初始化音频完成');
  }

  /**
   * 初始化积水
   */
  private initializePuddles(): void {
    // 如果未启用积水，则返回
    if (!this.enableRaindropPuddles) {
      return;
    }

    // 获取水体位置和尺寸
    const position = this.getPosition();
    const size = this.getSize();

    // 创建积水网格
    const puddleCount = Math.floor(size.width * size.depth / 10); // 每10平方米一个积水
    for (let i = 0; i < puddleCount; i++) {
      // 随机位置
      const x = position.x + (Math.random() - 0.5) * size.width * 0.8;
      const z = position.z + (Math.random() - 0.5) * size.depth * 0.8;

      // 创建积水网格
      const puddleSize = 0.5 + Math.random() * 1.5; // 0.5-2米大小
      const geometry = new THREE.CircleGeometry(puddleSize, 16);
      geometry.rotateX(-Math.PI / 2); // 水平放置

      // 创建积水材质
      const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(0x4488cc),
        transparent: true,
        opacity: 0.6,
        roughness: 0.1,
        metalness: 0.0,
        side: THREE.DoubleSide,
        depthWrite: false
      });

      // 创建网格
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.set(x, position.y + 0.01, z); // 略高于地面
      mesh.userData.size = puddleSize;
      mesh.userData.maxDepth = this.raindropPuddleMaxDepth;
      mesh.userData.currentDepth = 0;
      mesh.userData.evaporationRate = 0.01 + Math.random() * 0.02; // 蒸发速率

      // 添加到实体
      this.entity.getTransform().getObject3D().add(mesh);

      // 添加到积水列表
      this.puddleMeshes.push(mesh);
    }

    Debug.log('EnhancedRainWaterComponent', `初始化积水完成，共${puddleCount}个积水`);
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    // 如果未启用，则不更新
    if (!this.isEnabled()) {
      return;
    }

    // 开始性能监控
    this.performanceMonitor.beginMeasure('enhancedRainWaterUpdate');

    // 更新雨滴生成
    this.updateRaindropGeneration(deltaTime);

    // 更新雨滴
    this.updateEnhancedRaindrops(deltaTime);

    // 更新积水
    if (this.enableRaindropPuddles) {
      this.updatePuddles(deltaTime);
    }

    // 结束性能监控
    this.performanceMonitor.endMeasure('enhancedRainWaterUpdate');
  }

  /**
   * 更新雨滴生成
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateRaindropGeneration(deltaTime: number): void {
    // 更新雨滴生成计时器
    this.raindropGenerationTimer += deltaTime;

    // 如果达到生成间隔，生成雨滴
    if (this.raindropGenerationTimer >= this.raindropGenerationInterval) {
      this.raindropGenerationTimer = 0;
      this.generateEnhancedRaindrops();
    }
  }

  /**
   * 生成增强雨滴
   */
  private generateEnhancedRaindrops(): void {
    // 如果未启用或没有水体实例化渲染器，则返回
    if (!this.isEnabled() || !this.waterInstancedRenderer) {
      return;
    }

    // 如果已达到最大雨滴数量，则返回
    if (this.currentRaindropCount >= this.maxRaindrops) {
      return;
    }

    // 计算雨滴数量
    const raindropCount = this.calculateRaindropCount();

    // 生成雨滴
    for (let i = 0; i < raindropCount; i++) {
      // 如果已达到最大雨滴数量，则返回
      if (this.currentRaindropCount >= this.maxRaindrops) {
        return;
      }

      // 随机位置
      const x = this.raindropGenerationArea.min.x + Math.random() * (this.raindropGenerationArea.max.x - this.raindropGenerationArea.min.x);
      const y = this.raindropGenerationArea.max.y; // 从顶部生成
      const z = this.raindropGenerationArea.min.z + Math.random() * (this.raindropGenerationArea.max.z - this.raindropGenerationArea.min.z);

      // 创建雨滴位置
      const position = new THREE.Vector3(x, y, z);

      // 创建雨滴
      this.createRaindrop(position);
    }
  }

  /**
   * 计算雨滴数量
   * @returns 雨滴数量
   */
  private calculateRaindropCount(): number {
    // 根据雨水类型计算雨滴数量
    const rainType = this.getRainWaterType();
    switch (rainType) {
      case RainWaterType.LIGHT:
        return Math.floor(5 + Math.random() * 5);
      case RainWaterType.MEDIUM:
        return Math.floor(10 + Math.random() * 10);
      case RainWaterType.HEAVY:
        return Math.floor(20 + Math.random() * 20);
      case RainWaterType.THUNDERSTORM:
        return Math.floor(30 + Math.random() * 30);
      case RainWaterType.MONSOON:
        return Math.floor(40 + Math.random() * 40);
      default:
        return Math.floor(10 + Math.random() * 10);
    }
  }

  /**
   * 创建雨滴
   * @param position 位置
   */
  private createRaindrop(position: THREE.Vector3): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 计算雨滴大小
    const size = this.calculateRaindropSize();

    // 计算雨滴速度
    const velocity = this.calculateRaindropVelocity();

    // 计算雨滴寿命
    const lifetime = this.calculateRaindropLifetime();

    // 创建雨滴特效
    const id = this.waterInstancedRenderer.createEffectInstance(
      WaterEffectType.DROPLET,
      position,
      {
        rotation: new THREE.Euler(0, Math.random() * Math.PI * 2, 0),
        scale: new THREE.Vector3(size, size, size),
        color: new THREE.Color(0x88ccff),
        opacity: 0.8,
        lifetime: lifetime,
        userData: {
          velocityX: velocity.x,
          velocityY: velocity.y,
          velocityZ: velocity.z,
          rotationSpeedX: Math.random() * 2.0,
          rotationSpeedY: Math.random() * 2.0,
          rotationSpeedZ: Math.random() * 2.0,
          deformationFactor: this.raindropDeformationFactor,
          shape: this.raindropShape,
          collided: false
        }
      }
    );

    // 添加到雨滴列表
    if (id) {
      this.raindropInstanceIds.push(id);
      this.currentRaindropCount++;
    }
  }

  /**
   * 计算雨滴大小
   * @returns 雨滴大小
   */
  private calculateRaindropSize(): number {
    // 根据雨水类型计算雨滴大小
    const rainType = this.getRainWaterType();
    let baseSize = 0.1;
    let randomRange = 0.05;

    switch (rainType) {
      case RainWaterType.LIGHT:
        baseSize = 0.05;
        randomRange = 0.02;
        break;
      case RainWaterType.MEDIUM:
        baseSize = 0.1;
        randomRange = 0.05;
        break;
      case RainWaterType.HEAVY:
        baseSize = 0.15;
        randomRange = 0.07;
        break;
      case RainWaterType.THUNDERSTORM:
        baseSize = 0.2;
        randomRange = 0.1;
        break;
      case RainWaterType.MONSOON:
        baseSize = 0.25;
        randomRange = 0.12;
        break;
      default:
        baseSize = 0.1;
        randomRange = 0.05;
        break;
    }

    return baseSize + Math.random() * randomRange;
  }

  /**
   * 计算雨滴速度
   * @returns 雨滴速度
   */
  private calculateRaindropVelocity(): THREE.Vector3 {
    // 基础下落速度
    const baseSpeed = 9.8; // 重力加速度

    // 根据雨水类型调整速度
    const rainType = this.getRainWaterType();
    let speedMultiplier = 1.0;

    switch (rainType) {
      case RainWaterType.LIGHT:
        speedMultiplier = 0.8;
        break;
      case RainWaterType.MEDIUM:
        speedMultiplier = 1.0;
        break;
      case RainWaterType.HEAVY:
        speedMultiplier = 1.2;
        break;
      case RainWaterType.THUNDERSTORM:
        speedMultiplier = 1.5;
        break;
      case RainWaterType.MONSOON:
        speedMultiplier = 1.8;
        break;
      default:
        speedMultiplier = 1.0;
        break;
    }

    // 计算下落速度
    const fallSpeed = baseSpeed * speedMultiplier;

    // 创建速度向量
    const velocity = new THREE.Vector3(0, -fallSpeed, 0);

    // 如果启用风力影响，添加风力
    if (this.enableWindEffect && this.windStrength > 0) {
      velocity.x = this.windDirection.x * this.windStrength;
      velocity.z = this.windDirection.y * this.windStrength;
    }

    return velocity;
  }

  /**
   * 计算雨滴寿命
   * @returns 雨滴寿命
   */
  private calculateRaindropLifetime(): number {
    // 根据雨水类型计算雨滴寿命
    const rainType = this.getRainWaterType();
    let baseLifetime = 2.0;
    let randomRange = 1.0;

    switch (rainType) {
      case RainWaterType.LIGHT:
        baseLifetime = 3.0;
        randomRange = 1.5;
        break;
      case RainWaterType.MEDIUM:
        baseLifetime = 2.0;
        randomRange = 1.0;
        break;
      case RainWaterType.HEAVY:
        baseLifetime = 1.5;
        randomRange = 0.7;
        break;
      case RainWaterType.THUNDERSTORM:
        baseLifetime = 1.0;
        randomRange = 0.5;
        break;
      case RainWaterType.MONSOON:
        baseLifetime = 0.8;
        randomRange = 0.4;
        break;
      default:
        baseLifetime = 2.0;
        randomRange = 1.0;
        break;
    }

    return baseLifetime + Math.random() * randomRange;
  }

  /**
   * 更新增强雨滴
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateEnhancedRaindrops(deltaTime: number): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 遍历所有雨滴
    for (let i = this.raindropInstanceIds.length - 1; i >= 0; i--) {
      const id = this.raindropInstanceIds[i];

      // 获取雨滴实例（简化处理，直接使用雨滴数据）
      const instance = {
        position: new THREE.Vector3(),
        rotation: new THREE.Euler(),
        scale: new THREE.Vector3(1, 1, 1),
        opacity: 1.0,
        getPosition: () => instance.position,
        userData: {
          velocity: new THREE.Vector3(0, -10, 0),
          velocityX: 0,
          velocityY: -10,
          velocityZ: 0,
          rotationSpeedX: 0,
          rotationSpeedY: 0,
          rotationSpeedZ: 0,
          lifetime: 5.0,
          age: 0,
          size: 0.05,
          deformation: 1.0,
          deformationFactor: 1.0,
          shape: RaindropShape.TEARDROP,
          collided: false
        }
      };

      // 如果雨滴已经碰撞，跳过
      if (instance.userData.collided) {
        continue;
      }

      // 更新雨滴位置
      instance.position.x += instance.userData.velocityX * deltaTime;
      instance.position.y += instance.userData.velocityY * deltaTime;
      instance.position.z += instance.userData.velocityZ * deltaTime;

      // 更新雨滴旋转
      instance.rotation.x += instance.userData.rotationSpeedX * deltaTime;
      instance.rotation.y += instance.userData.rotationSpeedY * deltaTime;
      instance.rotation.z += instance.userData.rotationSpeedZ * deltaTime;

      // 根据速度调整雨滴形状（速度越快，雨滴越扁平）
      if (instance.userData.shape === RaindropShape.TEARDROP || instance.userData.shape === RaindropShape.FLAT) {
        const verticalSpeed = Math.abs(instance.userData.velocityY);
        const flattenFactor = Math.min(1.0, verticalSpeed / 10.0) * instance.userData.deformationFactor;
        instance.scale.y = Math.max(0.1, 1.0 - flattenFactor * 0.5);
        instance.scale.x = 1.0 + flattenFactor * 0.3;
        instance.scale.z = 1.0 + flattenFactor * 0.3;
      }

      // 检测碰撞
      this.checkRaindropCollision(id, instance);

      // 如果雨滴超出范围，移除
      if (instance.position.y < this.raindropGenerationArea.min.y - 10) {
        this.removeRaindrop(id);
      }
    }
  }

  /**
   * 检测雨滴碰撞
   * @param id 雨滴ID
   * @param instance 雨滴实例
   */
  private checkRaindropCollision(id: string, instance: any): void {
    // 如果没有碰撞检测器，返回
    if (!this.raindropCollisionDetector) {
      return;
    }

    // 创建向下的方向向量
    const direction = new THREE.Vector3(0, -1, 0);

    // 检测碰撞
    const intersection = this.raindropCollisionDetector.detectCollision(
      instance.position,
      direction,
      0.5 // 检测距离
    );

    // 如果检测到碰撞
    if (intersection) {
      // 标记雨滴已碰撞
      instance.userData.collided = true;

      // 创建水花效果
      this.createSplashEffect(instance.position, intersection);

      // 创建涟漪效果
      if (this.enableRaindropRipples) {
        this.createRippleEffect(intersection.point);
      }

      // 更新积水
      if (this.enableRaindropPuddles) {
        this.addWaterToPuddle(intersection.point);
      }

      // 移除雨滴
      this.removeRaindrop(id);
    }
  }

  /**
   * 创建水花效果
   * @param _position 位置（未使用）
   * @param intersection 碰撞结果
   */
  private createSplashEffect(_position: THREE.Vector3, intersection: THREE.Intersection): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 计算水花大小
    const size = 0.1 + Math.random() * 0.2;

    // 创建水花特效
    this.waterInstancedRenderer.createEffectInstance(
      WaterEffectType.SPLASH,
      intersection.point,
      {
        rotation: new THREE.Euler(0, Math.random() * Math.PI * 2, 0),
        scale: new THREE.Vector3(size, size, size),
        color: new THREE.Color(0x88ccff),
        opacity: 0.8,
        lifetime: 0.3 + Math.random() * 0.3,
        userData: {
          velocityX: 0,
          velocityY: 0.5 + Math.random() * 0.5, // 向上的速度
          velocityZ: 0,
          rotationSpeedX: Math.random() * 5.0,
          rotationSpeedY: Math.random() * 5.0,
          rotationSpeedZ: Math.random() * 5.0
        }
      }
    );

    // 如果启用了雨滴声音，播放水花声音
    if (this.enableRaindropSounds && this.audioSystem) {
      // 简化音频播放，使用现有的音频源
      const audioSource = this.audioSystem.getSource(this.audioSourceId);
      if (audioSource) {
        audioSource.setVolume(0.1 * this.raindropSoundVolume);
        audioSource.play();
      }
    }
  }

  /**
   * 创建涟漪效果
   * @param position 位置
   */
  private createRippleEffect(position: THREE.Vector3): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 计算涟漪大小
    const size = 0.2 + Math.random() * 0.3;

    // 创建涟漪特效
    const id = this.waterInstancedRenderer.createEffectInstance(
      WaterEffectType.RIPPLE,
      new THREE.Vector3(position.x, position.y + 0.01, position.z), // 略高于地面
      {
        rotation: new THREE.Euler(-Math.PI / 2, 0, 0), // 水平放置
        scale: new THREE.Vector3(size, size, size),
        color: new THREE.Color(0xffffff),
        opacity: 0.5 * this.raindropRippleStrength,
        lifetime: 1.0 + Math.random() * 0.5,
        userData: {
          initialSize: size,
          targetSize: size * 3, // 最终尺寸是初始尺寸的3倍
          growSpeed: 1.0 + Math.random() * 0.5,
          fadeSpeed: 0.5 + Math.random() * 0.5
        }
      }
    );

    // 添加到涟漪列表
    if (id) {
      this.raindropRippleInstanceIds.push(id);
    }
  }

  /**
   * 添加水到积水
   * @param position 位置
   */
  private addWaterToPuddle(position: THREE.Vector3): void {
    // 如果未启用积水，则返回
    if (!this.enableRaindropPuddles) {
      return;
    }

    // 查找最近的积水
    let nearestPuddle: THREE.Mesh | null = null;
    let nearestDistance = Infinity;

    for (const puddle of this.puddleMeshes) {
      const distance = puddle.position.distanceTo(position);
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestPuddle = puddle;
      }
    }

    // 如果找到积水，并且距离在积水半径内，增加积水深度
    if (nearestPuddle && nearestDistance < nearestPuddle.userData.size) {
      // 增加积水深度
      nearestPuddle.userData.currentDepth = Math.min(
        nearestPuddle.userData.maxDepth,
        nearestPuddle.userData.currentDepth + 0.001
      );

      // 更新积水材质
      const material = nearestPuddle.material as THREE.MeshStandardMaterial;
      material.opacity = 0.3 + 0.5 * (nearestPuddle.userData.currentDepth / nearestPuddle.userData.maxDepth);
    }
  }

  /**
   * 更新积水
   * @param deltaTime 帧间隔时间（秒）
   */
  private updatePuddles(deltaTime: number): void {
    // 如果未启用积水，则返回
    if (!this.enableRaindropPuddles) {
      return;
    }

    // 遍历所有积水
    for (const puddle of this.puddleMeshes) {
      // 蒸发积水
      puddle.userData.currentDepth = Math.max(
        0,
        puddle.userData.currentDepth - puddle.userData.evaporationRate * deltaTime
      );

      // 更新积水材质
      const material = puddle.material as THREE.MeshStandardMaterial;
      material.opacity = 0.3 + 0.5 * (puddle.userData.currentDepth / puddle.userData.maxDepth);

      // 如果积水深度为0，隐藏积水
      puddle.visible = puddle.userData.currentDepth > 0;
    }
  }

  /**
   * 移除雨滴
   * @param id 雨滴ID
   */
  private removeRaindrop(id: string): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 移除雨滴实例
    this.waterInstancedRenderer.destroyEffectInstance(id);

    // 从列表中移除
    const index = this.raindropInstanceIds.indexOf(id);
    if (index !== -1) {
      this.raindropInstanceIds.splice(index, 1);
      this.currentRaindropCount = Math.max(0, this.currentRaindropCount - 1);
    }
  }

  /**
   * 设置雨水类型
   * @param type 雨水类型
   */
  public setRainWaterType(type: RainWaterType): void {
    super.setRainWaterType(type);

    // 更新雨滴生成间隔
    this.updateRaindropGenerationInterval();

    // 更新音频
    this.updateEnhancedAudio();
  }

  /**
   * 更新增强音频
   */
  private updateEnhancedAudio(): void {
    // 如果未启用雨滴声音或没有音频系统，则返回
    if (!this.enableRaindropSounds || !this.audioSystem || !this.audioSourceId) {
      return;
    }

    // 根据雨水类型选择音频
    let audioFile = 'sounds/rain_medium.mp3';
    const rainType = this.getRainWaterType();
    switch (rainType) {
      case RainWaterType.LIGHT:
        audioFile = 'sounds/rain_light.mp3';
        break;
      case RainWaterType.MEDIUM:
        audioFile = 'sounds/rain_medium.mp3';
        break;
      case RainWaterType.HEAVY:
        audioFile = 'sounds/rain_heavy.mp3';
        break;
      case RainWaterType.THUNDERSTORM:
        audioFile = 'sounds/rain_thunderstorm.mp3';
        break;
      case RainWaterType.MONSOON:
        audioFile = 'sounds/rain_monsoon.mp3';
        break;
      default:
        audioFile = 'sounds/rain_medium.mp3';
        break;
    }

    // 播放雨声
    this.audioSystem.play(this.audioSourceId, audioFile, {
      loop: true,
      volume: this.raindropSoundVolume
    });
  }

  /**
   * 设置风力强度
   * @param strength 风力强度
   */
  public setWindStrength(strength: number): void {
    this.windStrength = strength;
  }

  /**
   * 设置风力方向
   * @param direction 风力方向
   */
  public setWindDirection(direction: THREE.Vector2): void {
    this.windDirection.copy(direction);
  }

  /**
   * 设置是否启用风力影响
   * @param enable 是否启用
   */
  public setEnableWindEffect(enable: boolean): void {
    this.enableWindEffect = enable;
  }

  /**
   * 设置雨滴形状
   * @param shape 雨滴形状
   */
  public setRaindropShape(shape: RaindropShape): void {
    this.raindropShape = shape;
  }

  /**
   * 设置雨滴变形系数
   * @param factor 变形系数
   */
  public setRaindropDeformationFactor(factor: number): void {
    this.raindropDeformationFactor = factor;
  }

  /**
   * 设置雨滴碰撞精度
   * @param precision 碰撞精度
   */
  public setRaindropCollisionPrecision(precision: number): void {
    this.raindropCollisionPrecision = precision;
    if (this.raindropCollisionDetector) {
      this.raindropCollisionDetector.setPrecision(precision);
    }
  }

  /**
   * 设置是否启用雨滴轨迹
   * @param enable 是否启用
   */
  public setEnableRaindropTrails(enable: boolean): void {
    this.enableRaindropTrails = enable;
  }

  /**
   * 设置雨滴轨迹长度
   * @param length 轨迹长度
   */
  public setRaindropTrailLength(length: number): void {
    this.raindropTrailLength = length;
  }

  /**
   * 设置是否启用雨滴涟漪
   * @param enable 是否启用
   */
  public setEnableRaindropRipples(enable: boolean): void {
    this.enableRaindropRipples = enable;
  }

  /**
   * 设置雨滴涟漪强度
   * @param strength 涟漪强度
   */
  public setRaindropRippleStrength(strength: number): void {
    this.raindropRippleStrength = strength;
  }

  /**
   * 设置是否启用雨滴积水
   * @param enable 是否启用
   */
  public setEnableRaindropPuddles(enable: boolean): void {
    this.enableRaindropPuddles = enable;

    // 如果启用积水，但还没有初始化，则初始化积水
    if (enable && this.puddleMeshes.length === 0) {
      this.initializePuddles();
    }

    // 如果禁用积水，隐藏所有积水
    if (!enable) {
      for (const puddle of this.puddleMeshes) {
        puddle.visible = false;
      }
    }
  }

  /**
   * 设置雨滴积水最大深度
   * @param depth 最大深度
   */
  public setRaindropPuddleMaxDepth(depth: number): void {
    this.raindropPuddleMaxDepth = depth;

    // 更新所有积水的最大深度
    for (const puddle of this.puddleMeshes) {
      puddle.userData.maxDepth = depth;
    }
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 调用父类销毁
    super.dispose();

    // 清理雨滴
    this.clearRaindrops();

    // 清理积水
    this.clearPuddles();

    // 清理音频
    this.clearAudio();
  }

  /**
   * 清理雨滴
   */
  private clearRaindrops(): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 移除所有雨滴实例
    for (const id of this.raindropInstanceIds) {
      this.waterInstancedRenderer.destroyEffectInstance(id);
    }

    // 清空列表
    this.raindropInstanceIds = [];
    this.currentRaindropCount = 0;
  }

  /**
   * 清理积水
   */
  private clearPuddles(): void {
    // 移除所有积水网格
    for (const puddle of this.puddleMeshes) {
      this.entity.getTransform().getObject3D().remove(puddle);
    }

    // 清空列表
    this.puddleMeshes = [];
  }

  /**
   * 清理音频
   */
  private clearAudio(): void {
    // 如果没有音频系统或音频源ID，返回
    if (!this.audioSystem || !this.audioSourceId) {
      return;
    }

    // 停止音频
    this.audioSystem.stop(this.audioSourceId);

    // 移除音频源
    this.audioSystem.removeSource(this.audioSourceId);

    // 清空音频源ID
    this.audioSourceId = '';
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    const size = this.getSize();
    const position = this.getPosition();
    const color = this.getColor();

    return new EnhancedRainWaterComponent(this.entity!, {
      width: size.width,
      height: size.height,
      depth: size.depth,
      position: position,
      color: color,
      opacity: this.getOpacity(),
      flowSpeed: this.getFlowSpeed(),
      rainWaterType: this.getRainWaterType(),
      rainIntensity: this.getRainIntensity(),
      raindropLifetime: this.getRaindropLifetime(),
      raindropShape: this.raindropShape,
      raindropDeformationFactor: this.raindropDeformationFactor,
      raindropCollisionPrecision: this.raindropCollisionPrecision,
      enableWindEffect: this.enableWindEffect,
      windStrength: this.windStrength,
      windDirection: this.windDirection,
      enableRaindropTrails: this.enableRaindropTrails,
      raindropTrailLength: this.raindropTrailLength,
      enableRaindropRipples: this.enableRaindropRipples,
      raindropRippleStrength: this.raindropRippleStrength,
      enableRaindropPuddles: this.enableRaindropPuddles,
      raindropPuddleMaxDepth: this.raindropPuddleMaxDepth,
      enableRaindropSounds: this.enableRaindropSounds,
      raindropSoundVolume: this.raindropSoundVolume,
      enableRaindropOptimization: this.enableRaindropOptimization,
      raindropLODDistances: this.raindropLODDistances,
      enableGPUAcceleration: this.enableGPUAcceleration,
      enabled: this.isEnabled()
    });
  }
}
