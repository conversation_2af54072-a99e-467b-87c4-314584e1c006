/**
 * 雨水水体组件
 * 用于表示雨水水体及其特殊物理属性和效果
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';
import { AudioSource, AudioSourceOptions } from '../../audio/AudioSource';
import { AudioType } from '../../audio/AudioSystem';
import { UnderwaterParticleSystem, UnderwaterParticleType } from '../../rendering/water/UnderwaterParticleSystem';
import { WaterInstancedRenderer, WaterEffectType } from '../../rendering/water/WaterInstancedRenderer';
import { SimplexNoise } from '../../utils/SimplexNoise';

/**
 * 雨水类型
 */
export enum RainWaterType {
  /** 轻雨 */
  LIGHT = 'light',
  /** 中雨 */
  MEDIUM = 'medium',
  /** 暴雨 */
  HEAVY = 'heavy',
  /** 雷雨 */
  THUNDERSTORM = 'thunderstorm',
  /** 季风雨 */
  MONSOON = 'monsoon'
}

/**
 * 雨水水体配置
 */
export interface RainWaterConfig {
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
  /** 深度 */
  depth?: number;
  /** 位置 */
  position?: THREE.Vector3;
  /** 旋转 */
  rotation?: THREE.Euler;
  /** 颜色 */
  color?: THREE.Color;
  /** 不透明度 */
  opacity?: number;
  /** 流速 */
  flowSpeed?: number;
  /** 雨水类型 */
  rainWaterType?: RainWaterType;
  /** 雨强度 */
  rainIntensity?: number;
  /** 雨滴大小 */
  raindropSize?: number;
  /** 雨滴频率 */
  raindropFrequency?: number;
  /** 雨滴寿命 */
  raindropLifetime?: number;
  /** 是否启用水花效果 */
  enableSplashEffect?: boolean;
  /** 水花效果强度 */
  splashEffectStrength?: number;
  /** 是否启用水波纹效果 */
  enableRippleEffect?: boolean;
  /** 水波纹效果强度 */
  rippleEffectStrength?: number;
  /** 是否启用水流效果 */
  enableFlowEffect?: boolean;
  /** 水流效果强度 */
  flowEffectStrength?: number;
  /** 是否启用声音效果 */
  enableSoundEffect?: boolean;
  /** 声音效果音量 */
  soundEffectVolume?: number;
  /** 是否启用水流动力学 */
  enableFluidDynamics?: boolean;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 雨水水体组件
 */
export class RainWaterComponent extends WaterBodyComponent {
  /** 雨水类型 */
  private rainWaterType: RainWaterType = RainWaterType.MEDIUM;
  /** 雨强度 */
  private rainIntensity: number = 1.0;
  /** 雨滴大小 */
  private raindropSize: number = 0.1;
  /** 雨滴频率 */
  private raindropFrequency: number = 1.0;
  /** 雨滴寿命 */
  private raindropLifetime: number = 1.0;
  /** 是否启用水花效果 */
  private enableSplashEffect: boolean = true;
  /** 水花效果强度 */
  private splashEffectStrength: number = 1.0;
  /** 是否启用水波纹效果 */
  private enableRippleEffect: boolean = true;
  /** 水波纹效果强度 */
  private rippleEffectStrength: number = 1.0;
  /** 是否启用水流效果 */
  private enableFlowEffect: boolean = true;
  /** 水流效果强度 */
  private flowEffectStrength: number = 1.0;
  /** 是否启用声音效果 */
  private enableSoundEffect: boolean = true;
  /** 声音效果音量 */
  private soundEffectVolume: number = 1.0;
  /** 是否启用水流动力学 */
  private enableFluidDynamics: boolean = true;
  /** 音频源 */
  private audioSource: AudioSource | null = null;
  /** 雨滴粒子系统 */
  private raindropParticleSystem: any = null;
  /** 水花粒子系统 */
  private splashParticleSystem: any = null;
  /** 水波纹粒子系统 */
  private rippleParticleSystem: any = null;
  /** 噪声生成器 */
  private noiseGenerator: SimplexNoise = new SimplexNoise();
  /** 雨滴计时器 */
  private raindropTimer: number = 0;
  /** 水流网格 */
  private flowMesh: THREE.Mesh | null = null;

  /**
   * 创建雨水水体组件
   * @param entity 实体
   * @param config 雨水配置
   */
  constructor(entity: Entity, config: RainWaterConfig = {}) {
    // 确保水体类型设置为雨水
    const rainWaterConfig = {
      ...config,
      type: WaterBodyType.RAIN_WATER
    };

    super(entity, rainWaterConfig);

    // 应用配置
    this.applyConfig(config);

    // 初始化雨水特有属性
    this.initialize();
  }

  /**
   * 获取雨水类型
   * @returns 雨水类型
   */
  public getRainWaterType(): RainWaterType {
    return this.rainWaterType;
  }

  /**
   * 设置雨水类型
   * @param type 雨水类型
   */
  public setRainWaterType(type: RainWaterType): void {
    this.rainWaterType = type;
    this.updateRainParameters();
  }

  /**
   * 获取雨强度
   * @returns 雨强度
   */
  public getRainIntensity(): number {
    return this.rainIntensity;
  }

  /**
   * 设置雨强度
   * @param intensity 雨强度
   */
  public setRainIntensity(intensity: number): void {
    this.rainIntensity = intensity;
  }

  /**
   * 初始化雨水组件
   */
  public initialize(): void {
    // 调用父类初始化
    super.initialize();

    // 设置默认流向为向下
    if (!this.getFlowDirection()) {
      this.setFlowDirection(new THREE.Vector3(0, -1, 0));
    }

    // 设置默认流速
    if (this.getFlowSpeed() === 0) {
      this.setFlowSpeed(1.0);
    }

    // 根据雨水类型更新参数
    this.updateRainParameters();

    // 创建水流网格
    this.createFlowMesh();

    // 初始化音频
    this.initializeAudio();

    // 初始化粒子系统
    this.initializeParticleSystems();

    // 创建专用粒子系统
    this.createRaindropParticleSystem();
    this.createSplashParticleSystem();
    this.createRippleParticleSystem();

    Debug.log('RainWaterComponent', '雨水组件初始化完成');
  }

  /**
   * 应用配置
   * @param config 雨水配置
   */
  private applyConfig(config: RainWaterConfig): void {
    // 设置尺寸
    if (config.width !== undefined || config.height !== undefined || config.depth !== undefined) {
      this.setSize({
        width: config.width || 10,
        height: config.height || 0.1,
        depth: config.depth || 10
      });
    }

    // 设置位置
    if (config.position) {
      this.setPosition(config.position);
    }

    // 设置旋转
    if (config.rotation) {
      this.setRotation(config.rotation);
    }

    // 设置颜色
    if (config.color) {
      this.setColor(config.color);
    } else {
      this.setColor(new THREE.Color(0x88aaff));
    }

    // 设置不透明度
    if (config.opacity !== undefined) {
      this.setOpacity(config.opacity);
    } else {
      this.setOpacity(0.7);
    }

    // 设置流速
    if (config.flowSpeed !== undefined) {
      this.setFlowSpeed(config.flowSpeed);
    } else {
      this.setFlowSpeed(1.0);
    }

    // 设置雨水类型
    if (config.rainWaterType !== undefined) {
      this.rainWaterType = config.rainWaterType;
    }

    // 设置雨强度
    if (config.rainIntensity !== undefined) {
      this.rainIntensity = config.rainIntensity;
    }

    // 设置雨滴大小
    if (config.raindropSize !== undefined) {
      this.raindropSize = config.raindropSize;
    }

    // 设置雨滴频率
    if (config.raindropFrequency !== undefined) {
      this.raindropFrequency = config.raindropFrequency;
    }

    // 设置雨滴寿命
    if (config.raindropLifetime !== undefined) {
      this.raindropLifetime = config.raindropLifetime;
    }

    // 设置是否启用水花效果
    if (config.enableSplashEffect !== undefined) {
      this.enableSplashEffect = config.enableSplashEffect;
    }

    // 设置水花效果强度
    if (config.splashEffectStrength !== undefined) {
      this.splashEffectStrength = config.splashEffectStrength;
    }

    // 设置是否启用水波纹效果
    if (config.enableRippleEffect !== undefined) {
      this.enableRippleEffect = config.enableRippleEffect;
    }

    // 设置水波纹效果强度
    if (config.rippleEffectStrength !== undefined) {
      this.rippleEffectStrength = config.rippleEffectStrength;
    }

    // 设置是否启用水流效果
    if (config.enableFlowEffect !== undefined) {
      this.enableFlowEffect = config.enableFlowEffect;
    }

    // 设置水流效果强度
    if (config.flowEffectStrength !== undefined) {
      this.flowEffectStrength = config.flowEffectStrength;
    }

    // 设置是否启用声音效果
    if (config.enableSoundEffect !== undefined) {
      this.enableSoundEffect = config.enableSoundEffect;
    }

    // 设置声音效果音量
    if (config.soundEffectVolume !== undefined) {
      this.soundEffectVolume = config.soundEffectVolume;
    }

    // 设置是否启用水流动力学
    if (config.enableFluidDynamics !== undefined) {
      this.enableFluidDynamics = config.enableFluidDynamics;
    }

    // 设置是否启用
    if (config.enabled !== undefined) {
      this.setEnabled(config.enabled);
    }
  }

  /**
   * 更新雨水参数
   * 根据雨水类型设置相应的参数
   */
  private updateRainParameters(): void {
    switch (this.rainWaterType) {
      case RainWaterType.LIGHT:
        this.raindropSize = 0.05;
        this.raindropFrequency = 0.5;
        this.raindropLifetime = 0.8;
        this.splashEffectStrength = 0.5;
        this.rippleEffectStrength = 0.7;
        this.flowEffectStrength = 0.5;
        this.soundEffectVolume = 0.5;
        break;
      case RainWaterType.MEDIUM:
        this.raindropSize = 0.1;
        this.raindropFrequency = 1.0;
        this.raindropLifetime = 1.0;
        this.splashEffectStrength = 1.0;
        this.rippleEffectStrength = 1.0;
        this.flowEffectStrength = 1.0;
        this.soundEffectVolume = 0.8;
        break;
      case RainWaterType.HEAVY:
        this.raindropSize = 0.15;
        this.raindropFrequency = 2.0;
        this.raindropLifetime = 1.2;
        this.splashEffectStrength = 1.5;
        this.rippleEffectStrength = 1.2;
        this.flowEffectStrength = 1.5;
        this.soundEffectVolume = 1.0;
        break;
      case RainWaterType.THUNDERSTORM:
        this.raindropSize = 0.2;
        this.raindropFrequency = 3.0;
        this.raindropLifetime = 1.5;
        this.splashEffectStrength = 2.0;
        this.rippleEffectStrength = 1.5;
        this.flowEffectStrength = 2.0;
        this.soundEffectVolume = 1.2;
        break;
      case RainWaterType.MONSOON:
        this.raindropSize = 0.25;
        this.raindropFrequency = 4.0;
        this.raindropLifetime = 2.0;
        this.splashEffectStrength = 2.5;
        this.rippleEffectStrength = 2.0;
        this.flowEffectStrength = 2.5;
        this.soundEffectVolume = 1.5;
        break;
      default:
        this.raindropSize = 0.1;
        this.raindropFrequency = 1.0;
        this.raindropLifetime = 1.0;
        this.splashEffectStrength = 1.0;
        this.rippleEffectStrength = 1.0;
        this.flowEffectStrength = 1.0;
        this.soundEffectVolume = 0.8;
        break;
    }
  }

  /**
   * 创建水流网格
   */
  private createFlowMesh(): void {
    // 获取水体尺寸
    const size = this.getSize();

    // 获取水体位置
    const position = this.getPosition();

    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(size.width, size.depth, 32, 32);
    geometry.rotateX(-Math.PI / 2); // 使平面水平

    // 创建水流材质
    const material = new THREE.MeshStandardMaterial({
      color: this.getColor(),
      transparent: true,
      opacity: this.getOpacity(),
      side: THREE.DoubleSide,
      roughness: 0.2,
      metalness: 0.1
    });

    // 加载法线贴图
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load('/assets/textures/water_normal.jpg', (texture) => {
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.repeat.set(4, 4);
      material.normalMap = texture;
      material.normalScale.set(0.5, 0.5);
    });

    // 创建网格
    this.flowMesh = new THREE.Mesh(geometry, material);
    this.flowMesh.position.copy(position);

    // 保存原始顶点位置（用于波动效果）
    const positions = geometry.attributes.position.array;
    geometry.userData.originalPositions = new Float32Array(positions.length);
    for (let i = 0; i < positions.length; i++) {
      geometry.userData.originalPositions[i] = positions[i];
    }
    geometry.userData.time = 0;

    // 添加到实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any;
    if (transform) {
      transform.getObject3D().add(this.flowMesh);
    }

    Debug.log('RainWaterComponent', '创建水流网格完成');
  }

  /**
   * 初始化音频
   */
  private initializeAudio(): void {
    // 如果未启用声音效果，则不初始化音频
    if (!this.enableSoundEffect) return;

    // 获取音频系统
    try {
      const world = this.entity.getWorld();
      if (!world) {
        Debug.warn('RainWaterComponent', '实体未关联到世界');
        return;
      }

      const systems = world.getSystems();
      const audioSystem = systems.find(system => system.getType() === 'AudioSystem');
      if (!audioSystem) {
        Debug.warn('RainWaterComponent', '未找到音频系统');
        return;
      }

      // 创建音频源
      const audioSourceId = `rainwater_${this.entity.id}`;
      this.audioSource = (audioSystem as any).createSource(audioSourceId, AudioType.AMBIENT);
      if (!this.audioSource) {
        Debug.warn('RainWaterComponent', '创建音频源失败');
        return;
      }

      // 设置音频源属性
      this.audioSource.setVolume(this.soundEffectVolume);
      this.audioSource.setLoop(true);
      this.audioSource.setSpatial(true);
      const position = this.getPosition();
      this.audioSource.setPosition(position.x, position.y, position.z);
      this.audioSource.setRefDistance(10);
      this.audioSource.setMaxDistance(100);
      this.audioSource.setRolloffFactor(1);

      // 根据雨水类型选择音频
      let audioFile = 'sounds/rain_medium.mp3';
      switch (this.rainWaterType) {
        case RainWaterType.LIGHT:
          audioFile = 'sounds/rain_light.mp3';
          break;
        case RainWaterType.MEDIUM:
          audioFile = 'sounds/rain_medium.mp3';
          break;
        case RainWaterType.HEAVY:
          audioFile = 'sounds/rain_heavy.mp3';
          break;
        case RainWaterType.THUNDERSTORM:
          audioFile = 'sounds/rain_thunderstorm.mp3';
          break;
        case RainWaterType.MONSOON:
          audioFile = 'sounds/rain_monsoon.mp3';
          break;
      }

      // 播放雨声
      (audioSystem as any).play(audioSourceId, audioFile, {
        loop: true,
        volume: this.soundEffectVolume,
        type: AudioType.AMBIENT
      });

      Debug.log('RainWaterComponent', '初始化音频完成');
    } catch (error) {
      Debug.error('RainWaterComponent', '音频初始化失败:', error);
    }
  }

  /**
   * 初始化粒子系统
   */
  private initializeParticleSystems(): void {
    // 如果未启用水花效果、水波纹效果和水流效果，则不初始化粒子系统
    if (!this.enableSplashEffect && !this.enableRippleEffect && !this.enableFlowEffect) return;

    try {
      // 获取水下粒子系统
      const world = this.entity.getWorld();
      if (!world) {
        Debug.warn('RainWaterComponent', '实体未关联到世界');
        return;
      }

      const systems = world.getSystems();
      const underwaterParticleSystem = systems.find(system => system.getType() === 'UnderwaterParticleSystem') as unknown as UnderwaterParticleSystem;
      if (!underwaterParticleSystem) {
        Debug.warn('RainWaterComponent', '未找到水下粒子系统');
        return;
      }

    // 获取水体位置
    const position = this.getPosition();
    const size = this.getSize();

    // 创建水花效果
    if (this.enableSplashEffect) {
      underwaterParticleSystem.addParticleGroup(
        this.entity.id,
        'rainwaterSplash',
        {
          type: UnderwaterParticleType.SPLASH,
          count: Math.floor(100 * this.splashEffectStrength * this.rainIntensity),
          size: [0.05, 0.15],
          color: 0xffffff,
          opacity: 0.8 * this.splashEffectStrength,
          lifetime: [0.5, 1.0],
          speed: [0.5, 1.5],
          acceleration: new THREE.Vector3(0, -9.8, 0),
          rotation: true,
          rotationSpeed: [1.0, 3.0],
          blending: THREE.AdditiveBlending,
          emissionArea: {
            shape: 'box',
            size: new THREE.Vector3(size.width, 0.1, size.depth),
            position: new THREE.Vector3(position.x, position.y + 0.05, position.z)
          }
        }
      );
    }

    // 创建水波纹效果
    if (this.enableRippleEffect) {
      underwaterParticleSystem.addParticleGroup(
        this.entity.id,
        'rainwaterRipple',
        {
          type: UnderwaterParticleType.RIPPLE,
          count: Math.floor(50 * this.rippleEffectStrength * this.rainIntensity),
          size: [0.2, 0.5],
          color: 0xffffff,
          opacity: 0.6 * this.rippleEffectStrength,
          lifetime: [1.0, 2.0],
          speed: [0.0, 0.0],
          rotation: false,
          blending: THREE.AdditiveBlending,
          emissionArea: {
            shape: 'box',
            size: new THREE.Vector3(size.width * 0.9, 0.1, size.depth * 0.9),
            position: new THREE.Vector3(position.x, position.y, position.z)
          }
        }
      );
    }

    // 创建水流效果
    if (this.enableFlowEffect) {
      underwaterParticleSystem.addParticleGroup(
        this.entity.id,
        'rainwaterFlow',
        {
          type: UnderwaterParticleType.WATER_CURRENT,
          count: Math.floor(30 * this.flowEffectStrength),
          size: [0.1, 0.3],
          color: this.getColor(),
          opacity: 0.5 * this.flowEffectStrength,
          lifetime: [2.0, 4.0],
          speed: [0.5, 1.0],
          acceleration: new THREE.Vector3(0, -1, 0),
          blending: THREE.AdditiveBlending,
          emissionArea: {
            shape: 'box',
            size: new THREE.Vector3(size.width, 0.5, size.depth),
            position: new THREE.Vector3(position.x, position.y + 0.25, position.z)
          }
        }
      );
    }

      Debug.log('RainWaterComponent', '初始化粒子系统完成');
    } catch (error) {
      Debug.error('RainWaterComponent', '粒子系统初始化失败:', error);
    }
  }

  /**
   * 更新雨水组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    // 如果未启用，则不更新
    if (!this.isEnabled()) return;

    // 更新雨滴生成
    this.updateRaindrops(deltaTime);

    // 更新水流动力学
    if (this.enableFluidDynamics) {
      this.updateFluidDynamics(deltaTime);
    }

    // 更新音频
    this.updateAudio(deltaTime);

    // 更新粒子效果
    this.updateParticleEffects(deltaTime);
  }

  /**
   * 更新雨滴生成
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateRaindrops(deltaTime: number): void {
    // 更新雨滴计时器
    this.raindropTimer += deltaTime;

    // 计算雨滴生成间隔
    const raindropInterval = 1.0 / (this.raindropFrequency * this.rainIntensity);

    // 如果达到生成间隔，生成雨滴
    if (this.raindropTimer >= raindropInterval) {
      this.raindropTimer = 0;
      this.generateRaindrops();
    }
  }

  /**
   * 生成雨滴
   */
  private generateRaindrops(): void {
    // 获取水体尺寸和位置
    const size = this.getSize();
    const position = this.getPosition();

    // 计算雨滴数量（根据雨强度和雨水类型动态调整）
    const raindropCount = Math.floor(5 * this.rainIntensity * this.getRaindropMultiplier());

    // 生成雨滴
    for (let i = 0; i < raindropCount; i++) {
      // 计算随机位置（使用更自然的分布和噪声）
      const distanceFromCenter = Math.random() * 0.5 * Math.sqrt(Math.random()) * size.width;
      const angle = Math.random() * Math.PI * 2;
      let x = position.x + Math.cos(angle) * distanceFromCenter;
      let z = position.z + Math.sin(angle) * distanceFromCenter;

      // 使用噪声生成器添加自然变化
      const noiseValue = this.generateRaindropNoise(x, z, this.raindropTimer);
      x += noiseValue * 0.5;
      z += noiseValue * 0.5;

      // 创建雨滴位置
      const raindropPosition = new THREE.Vector3(x, position.y, z);

      // 计算雨滴大小（添加随机变化）
      const dropSize = this.raindropSize * (0.8 + Math.random() * 0.4);

      // 计算雨滴速度（考虑风向和风速）
      const windEffect = this.getWindEffect();
      const dropVelocity = new THREE.Vector3(
        windEffect.x * (0.8 + Math.random() * 0.4),
        0.5 + Math.random() * 0.5,
        windEffect.z * (0.8 + Math.random() * 0.4)
      );

      // 计算碰撞强度（基于雨滴大小和速度）
      const impactStrength = dropSize * dropVelocity.y * 0.1;

      // 添加波纹（大小和强度基于碰撞强度）
      this.addRipple(raindropPosition, dropSize * 2, impactStrength);

      // 获取水下粒子系统
      try {
        const world = this.entity.getWorld();
        if (world) {
          const systems = world.getSystems();
          const underwaterParticleSystem = systems.find(system => system.getType() === 'UnderwaterParticleSystem') as unknown as UnderwaterParticleSystem;

          if (underwaterParticleSystem) {
            // 如果启用了水花效果，创建水花
            if (this.enableSplashEffect) {
              // 使用粒子系统的发射方法（如果存在）
              if (typeof (underwaterParticleSystem as any).emitParticle === 'function') {
                (underwaterParticleSystem as any).emitParticle(
                  this.entity.id,
                  'rainwaterSplash',
                  raindropPosition,
                  dropVelocity
                );
              }
            }

            // 如果启用了水波纹效果，创建水波纹
            if (this.enableRippleEffect) {
              if (typeof (underwaterParticleSystem as any).emitParticle === 'function') {
                (underwaterParticleSystem as any).emitParticle(
                  this.entity.id,
                  'rainwaterRipple',
                  new THREE.Vector3(raindropPosition.x, position.y + 0.01, raindropPosition.z),
                  new THREE.Vector3(0, 0, 0)
                );
              }
            }
          }
        }
      } catch (error) {
        // 静默处理粒子发射错误，不影响主要功能
      }
    }
  }

  /**
   * 获取雨滴乘数（基于雨水类型）
   * @returns 雨滴乘数
   */
  private getRaindropMultiplier(): number {
    switch (this.rainWaterType) {
      case RainWaterType.LIGHT:
        return 0.7;
      case RainWaterType.MEDIUM:
        return 1.0;
      case RainWaterType.HEAVY:
        return 1.5;
      case RainWaterType.THUNDERSTORM:
        return 2.0;
      case RainWaterType.MONSOON:
        return 2.5;
      default:
        return 1.0;
    }
  }

  /**
   * 获取风效应
   * @returns 风效应向量
   */
  private getWindEffect(): THREE.Vector3 {
    try {
      // 获取水体天气系统
      const world = this.entity.getWorld();
      if (!world) {
        return new THREE.Vector3(0.01, 0, 0.01);
      }

      const systems = world.getSystems();
      const weatherSystem = systems.find(system => system.getType() === 'WaterWeatherSystem');
      if (!weatherSystem) {
        // 如果没有天气系统，返回默认风效应
        return new THREE.Vector3(0.01, 0, 0.01);
      }

      // 获取风向和风速
      const config = (weatherSystem as any).config;
      if (!config) {
        return new THREE.Vector3(0.01, 0, 0.01);
      }

      const windDirection = config.windDirection || 0;
      const windSpeed = config.windSpeed || 0;

      // 计算风效应
      return new THREE.Vector3(
        Math.cos(windDirection) * windSpeed * 0.02,
        0,
        Math.sin(windDirection) * windSpeed * 0.02
      );
    } catch (error) {
      Debug.warn('RainWaterComponent', '获取风效应失败，使用默认值:', error);
      return new THREE.Vector3(0.01, 0, 0.01);
    }
  }

  /**
   * 更新水流动力学
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateFluidDynamics(deltaTime: number): void {
    // 如果没有水流网格，则不更新
    if (!this.flowMesh) return;

    // 获取水流材质
    const material = this.flowMesh.material as THREE.MeshStandardMaterial;
    if (!material) return;

    // 更新水流材质的位移贴图偏移，模拟水流动态
    if (!material.userData.time) {
      material.userData.time = 0;
    }
    material.userData.time += deltaTime * this.getFlowSpeed();

    // 如果材质有位移贴图，更新偏移
    if (material.displacementMap) {
      material.displacementMap.offset.y = -material.userData.time;
    }

    // 如果材质有法线贴图，更新偏移
    if (material.normalMap) {
      material.normalMap.offset.y = -material.userData.time;
    }

    // 更新水面波动
    this.updateWaterSurface(deltaTime);
  }

  /**
   * 更新水面波动
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterSurface(deltaTime: number): void {
    // 如果没有水流网格，则不更新
    if (!this.flowMesh) return;

    // 获取几何体
    const geometry = this.flowMesh.geometry as THREE.BufferGeometry;
    if (!geometry || !geometry.userData.originalPositions) return;

    // 获取顶点位置
    const positions = geometry.attributes.position.array as Float32Array;
    const count = positions.length / 3;

    // 更新时间
    if (!geometry.userData.time) {
      geometry.userData.time = 0;
    }
    geometry.userData.time += deltaTime * this.getFlowSpeed() * 0.5;
    const time = geometry.userData.time;

    // 获取风效应
    const windEffect = this.getWindEffect();
    const windStrength = Math.sqrt(windEffect.x * windEffect.x + windEffect.z * windEffect.z);

    // 获取雨强度
    const rainIntensity = this.rainIntensity;

    // 应用波动
    for (let i = 0; i < count; i++) {
      const index = i * 3;
      const originalX = geometry.userData.originalPositions[index];
      const originalY = geometry.userData.originalPositions[index + 1];
      const originalZ = geometry.userData.originalPositions[index + 2];

      // 使用多层正弦波模拟波动
      // 第一层：基础波动
      const waveX1 = Math.sin(originalX * 0.5 + time) * 0.05;
      const waveZ1 = Math.cos(originalZ * 0.5 + time * 0.8) * 0.05;

      // 第二层：风向影响的波动
      const waveX2 = Math.sin(originalX * 0.8 + time * windEffect.x * 2) * 0.03 * windStrength;
      const waveZ2 = Math.cos(originalZ * 0.8 + time * windEffect.z * 2) * 0.03 * windStrength;

      // 第三层：雨滴影响的小波纹
      const waveX3 = Math.sin(originalX * 3 + time * 2) * 0.01 * rainIntensity;
      const waveZ3 = Math.cos(originalZ * 3 + time * 1.5) * 0.01 * rainIntensity;

      // 使用噪声添加自然随机性
      const noiseValue = Math.sin(originalX * 2 + originalZ * 2 + time) * 0.01 * rainIntensity;

      // 组合所有波动效果
      const totalWave = waveX1 + waveZ1 + waveX2 + waveZ2 + waveX3 + waveZ3 + noiseValue;

      // 应用波动
      positions[index + 1] = originalY + totalWave;
    }

    // 更新几何体
    geometry.attributes.position.needsUpdate = true;
  }

  /**
   * 更新音频
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateAudio(deltaTime: number): void {
    // 如果未启用声音效果或没有音频源，则不更新
    if (!this.enableSoundEffect || !this.audioSource) return;

    try {
      // 更新音频源位置
      const position = this.getPosition();
      if (position && typeof this.audioSource.setPosition === 'function') {
        this.audioSource.setPosition(position.x, position.y, position.z);
      }

      // 更新音量（根据雨强度和时间变化）
      if (typeof this.audioSource.setVolume === 'function') {
        const volumeVariation = 1.0 + Math.sin(deltaTime * 2.0) * 0.1; // 轻微的音量变化
        const finalVolume = Math.max(0, Math.min(1, this.soundEffectVolume * this.rainIntensity * volumeVariation));
        this.audioSource.setVolume(finalVolume);
      }
    } catch (error) {
      Debug.warn('RainWaterComponent', '音频更新失败:', error);
    }
  }

  /**
   * 更新粒子效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateParticleEffects(deltaTime: number): void {
    // 如果未启用水花效果、水波纹效果和水流效果，则不更新
    if (!this.enableSplashEffect && !this.enableRippleEffect && !this.enableFlowEffect) return;

    try {
      // 获取水下粒子系统
      const world = this.entity.getWorld();
      if (!world) return;

      const systems = world.getSystems();
      const underwaterParticleSystem = systems.find(system => system.getType() === 'UnderwaterParticleSystem') as unknown as UnderwaterParticleSystem;
      if (!underwaterParticleSystem) return;

    // 获取水体位置
    const position = this.getPosition();

    // 更新粒子组位置（如果系统支持）
    if (typeof (underwaterParticleSystem as any).updateParticleGroupPosition === 'function') {
      if (this.enableSplashEffect) {
        (underwaterParticleSystem as any).updateParticleGroupPosition(
          this.entity.id,
          'rainwaterSplash',
          new THREE.Vector3(position.x, position.y + 0.05, position.z)
        );
      }

      if (this.enableRippleEffect) {
        (underwaterParticleSystem as any).updateParticleGroupPosition(
          this.entity.id,
          'rainwaterRipple',
          new THREE.Vector3(position.x, position.y, position.z)
        );
      }

      if (this.enableFlowEffect) {
        (underwaterParticleSystem as any).updateParticleGroupPosition(
          this.entity.id,
          'rainwaterFlow',
          new THREE.Vector3(position.x, position.y + 0.25, position.z)
        );
      }
    }

    // 根据雨强度和时间动态调整粒子发射率（如果系统支持）
    if (typeof (underwaterParticleSystem as any).updateParticleGroupEmissionRate === 'function') {
      // 使用时间变化创建更自然的效果
      const timeVariation = 1.0 + Math.sin(deltaTime * 3.0) * 0.2;

      if (this.enableSplashEffect) {
        (underwaterParticleSystem as any).updateParticleGroupEmissionRate(
          this.entity.id,
          'rainwaterSplash',
          Math.floor(100 * this.splashEffectStrength * this.rainIntensity * timeVariation)
        );
      }

      if (this.enableRippleEffect) {
        (underwaterParticleSystem as any).updateParticleGroupEmissionRate(
          this.entity.id,
          'rainwaterRipple',
          Math.floor(50 * this.rippleEffectStrength * this.rainIntensity * timeVariation)
        );
      }
    }
    } catch (error) {
      Debug.warn('RainWaterComponent', '粒子效果更新失败:', error);
    }
  }

  /**
   * 设置雨滴寿命
   * @param lifetime 寿命（秒）
   */
  public setRaindropLifetime(lifetime: number): void {
    this.raindropLifetime = Math.max(0.1, lifetime);
  }

  /**
   * 获取雨滴寿命
   * @returns 雨滴寿命（秒）
   */
  public getRaindropLifetime(): number {
    return this.raindropLifetime;
  }

  /**
   * 设置水花效果强度
   * @param strength 强度（0-1）
   */
  public setSplashEffectStrength(strength: number): void {
    this.splashEffectStrength = Math.max(0, Math.min(1, strength));
  }

  /**
   * 设置水波纹效果强度
   * @param strength 强度（0-1）
   */
  public setRippleEffectStrength(strength: number): void {
    this.rippleEffectStrength = Math.max(0, Math.min(1, strength));
  }

  /**
   * 设置水流效果强度
   * @param strength 强度（0-1）
   */
  public setFlowEffectStrength(strength: number): void {
    this.flowEffectStrength = Math.max(0, Math.min(1, strength));
  }

  /**
   * 使用噪声生成器创建自然的雨滴分布
   * @param x X坐标
   * @param z Z坐标
   * @param time 时间
   * @returns 噪声值
   */
  private generateRaindropNoise(x: number, z: number, time: number): number {
    return this.noiseGenerator.noise3D(x * 0.1, z * 0.1, time * 0.5);
  }

  /**
   * 创建雨滴粒子系统
   */
  private createRaindropParticleSystem(): void {
    // 这里可以创建专门的雨滴粒子系统
    this.raindropParticleSystem = {
      active: true,
      particleCount: 0,
      maxParticles: 1000
    };
  }

  /**
   * 创建水花粒子系统
   */
  private createSplashParticleSystem(): void {
    // 这里可以创建专门的水花粒子系统
    this.splashParticleSystem = {
      active: this.enableSplashEffect,
      particleCount: 0,
      maxParticles: 500
    };
  }

  /**
   * 创建水波纹粒子系统
   */
  private createRippleParticleSystem(): void {
    // 这里可以创建专门的水波纹粒子系统
    this.rippleParticleSystem = {
      active: this.enableRippleEffect,
      particleCount: 0,
      maxParticles: 200
    };
  }

  /**
   * 销毁组件，清理资源
   */
  public destroy(): void {
    try {
      // 停止音频
      if (this.audioSource) {
        if (typeof this.audioSource.stop === 'function') {
          this.audioSource.stop();
        }
        this.audioSource = null;
      }

      // 清理网格
      if (this.flowMesh) {
        if (this.flowMesh.geometry) {
          this.flowMesh.geometry.dispose();
        }
        if (this.flowMesh.material) {
          if (Array.isArray(this.flowMesh.material)) {
            this.flowMesh.material.forEach(material => (material as any).dispose());
          } else {
            this.flowMesh.material.dispose();
          }
        }

        // 从场景中移除
        const transform = this.entity.getComponent('Transform') as any as any;
        if (transform && transform.getObject3D()) {
          transform.getObject3D().remove(this.flowMesh);
        }

        this.flowMesh = null;
      }

      // 清理粒子系统
      try {
        const world = this.entity.getWorld();
        if (world) {
          const systems = world.getSystems();
          const underwaterParticleSystem = systems.find(system => system.getType() === 'UnderwaterParticleSystem');

          if (underwaterParticleSystem && typeof (underwaterParticleSystem as any).removeParticleGroup === 'function') {
            (underwaterParticleSystem as any).removeParticleGroup(this.entity.id, 'rainwaterSplash');
            (underwaterParticleSystem as any).removeParticleGroup(this.entity.id, 'rainwaterRipple');
            (underwaterParticleSystem as any).removeParticleGroup(this.entity.id, 'rainwaterFlow');
          }
        }
      } catch (error) {
        Debug.warn('RainWaterComponent', '清理粒子系统失败:', error);
      }

      // 重置状态
      this.raindropParticleSystem = null;
      this.splashParticleSystem = null;
      this.rippleParticleSystem = null;

      Debug.log('RainWaterComponent', '组件销毁完成');
    } catch (error) {
      Debug.error('RainWaterComponent', '组件销毁失败:', error);
    }

    // 父类没有 destroy 方法，无需调用
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    const size = this.getSize();
    const position = this.getPosition();
    const color = this.getColor();

    return new RainWaterComponent(this.entity!, {
      width: size.width,
      height: size.height,
      depth: size.depth,
      position: position,
      color: color,
      opacity: this.getOpacity(),
      flowSpeed: this.getFlowSpeed(),
      rainWaterType: this.rainWaterType,
      rainIntensity: this.rainIntensity,
      raindropSize: this.raindropSize,
      raindropFrequency: this.raindropFrequency,
      raindropLifetime: this.raindropLifetime,
      enableSplashEffect: this.enableSplashEffect,
      enableRippleEffect: this.enableRippleEffect,
      enableFlowEffect: this.enableFlowEffect,
      enableFluidDynamics: this.enableFluidDynamics,
      splashEffectStrength: this.splashEffectStrength,
      rippleEffectStrength: this.rippleEffectStrength,
      flowEffectStrength: this.flowEffectStrength,
      enableSoundEffect: this.enableSoundEffect,
      soundEffectVolume: this.soundEffectVolume,
      enabled: this.isEnabled()
    });
  }
}
