/**
 * 粒子发射器
 * 用于控制粒子的生成和行为
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
import type { Entity } from '../core/Entity';
import { Particle } from './Particle';
import { ParticleSystem } from './ParticleSystem';
import type { Transform } from '../scene/Transform';

/**
 * 粒子发射器形状类型
 */
export enum EmitterShapeType {
  /** 点发射器 */
  POINT = 'point',
  /** 球形发射器 */
  SPHERE = 'sphere',
  /** 圆形发射器 */
  CIRCLE = 'circle',
  /** 矩形发射器 */
  RECTANGLE = 'rectangle',
  /** 圆锥发射器 */
  CONE = 'cone',
  /** 圆环发射器 */
  TORUS = 'torus',
  /** 网格发射器 */
  MESH = 'mesh'
}

/**
 * 粒子发射器选项
 */
export interface ParticleEmitterOptions {
  /** 发射器名称 */
  name?: string;
  /** 发射器形状类型 */
  shapeType?: EmitterShapeType;
  /** 发射器形状参数 */
  shapeParams?: any;
  /** 粒子纹理 */
  texture?: THREE.Texture | string;
  /** 粒子材质 */
  material?: THREE.Material;
  /** 粒子大小 */
  particleSize?: number | [number, number];
  /** 粒子颜色 */
  particleColor?: THREE.Color | [THREE.Color, THREE.Color];
  /** 粒子透明度 */
  particleOpacity?: number | [number, number];
  /** 粒子生命周期（秒） */
  particleLifetime?: number | [number, number];
  /** 粒子速度 */
  particleVelocity?: number | [number, number];
  /** 粒子加速度 */
  particleAcceleration?: THREE.Vector3;
  /** 粒子旋转速度 */
  particleRotationSpeed?: number | [number, number];
  /** 粒子缩放速度 */
  particleScaleSpeed?: THREE.Vector2 | [THREE.Vector2, THREE.Vector2];
  /** 粒子发射率（每秒） */
  emissionRate?: number;
  /** 粒子发射角度 */
  emissionAngle?: number | [number, number];
  /** 粒子发射力度 */
  emissionForce?: number | [number, number];
  /** 粒子发射方向 */
  emissionDirection?: THREE.Vector3;
  /** 粒子发射扩散度 */
  emissionSpread?: number;
  /** 粒子重力 */
  gravity?: THREE.Vector3;
  /** 粒子阻力 */
  drag?: number;
  /** 是否启用碰撞 */
  enableCollision?: boolean;
  /** 是否启用粒子排序 */
  enableSorting?: boolean;
  /** 是否自动开始发射 */
  autoStart?: boolean;
  /** 发射持续时间（秒），0表示无限 */
  duration?: number;
  /** 最大粒子数量 */
  maxParticles?: number;
  /** 是否循环发射 */
  loop?: boolean;
  /** 爆发模式参数 */
  burst?: {
    /** 爆发数量 */
    count: number;
    /** 爆发间隔（秒） */
    interval: number;
    /** 爆发次数，0表示无限 */
    cycles: number;
  };
}

/**
 * 粒子发射器组件
 */
export class ParticleEmitter extends Component {
  /** 组件类型 */
  public static readonly type: string = 'ParticleEmitter';

  /** 发射器名称 */
  public name: string;

  /** 发射器形状类型 */
  public shapeType: EmitterShapeType;

  /** 发射器形状参数 */
  public shapeParams: any;

  /** 粒子纹理 */
  public texture: THREE.Texture | null = null;

  /** 粒子材质 */
  public material: THREE.Material | null = null;

  /** 粒子大小范围 */
  public particleSize: [number, number] = [1, 1];

  /** 粒子颜色范围 */
  public particleColor: [THREE.Color, THREE.Color] = [
    new THREE.Color().setRGB(1, 1, 1),
    new THREE.Color().setRGB(1, 1, 1)
  ];

  /** 粒子透明度范围 */
  public particleOpacity: [number, number] = [1, 0];

  /** 粒子生命周期范围（秒） */
  public particleLifetime: [number, number] = [1, 1];

  /** 粒子速度范围 */
  public particleVelocity: [number, number] = [1, 1];

  /** 粒子加速度 */
  public particleAcceleration: THREE.Vector3 = new THREE.Vector3();

  /** 粒子旋转速度范围 */
  public particleRotationSpeed: [number, number] = [0, 0];

  /** 粒子缩放速度范围 */
  public particleScaleSpeed: [THREE.Vector2, THREE.Vector2] = [
    new THREE.Vector2(0, 0),
    new THREE.Vector2(0, 0)
  ];

  /** 粒子发射率（每秒） */
  public emissionRate: number = 10;

  /** 粒子发射角度范围 */
  public emissionAngle: [number, number] = [0, Math.PI * 2];

  /** 粒子发射力度范围 */
  public emissionForce: [number, number] = [1, 1];

  /** 粒子发射方向 */
  public emissionDirection: THREE.Vector3 = new THREE.Vector3(0, 1, 0);

  /** 粒子发射扩散度 */
  public emissionSpread: number = 0;

  /** 粒子重力 */
  public gravity: THREE.Vector3 = new THREE.Vector3(0, -9.8, 0);

  /** 粒子阻力 */
  public drag: number = 0;

  /** 是否启用碰撞 */
  public enableCollision: boolean = false;

  /** 是否启用粒子排序 */
  public enableSorting: boolean = false;

  /** 是否自动开始发射 */
  public autoStart: boolean = true;

  /** 发射持续时间（秒），0表示无限 */
  public duration: number = 0;

  /** 最大粒子数量 */
  public maxParticles: number = 1000;

  /** 是否循环发射 */
  public loop: boolean = false;

  /** 爆发模式参数 */
  public burst: {
    /** 爆发数量 */
    count: number;
    /** 爆发间隔（秒） */
    interval: number;
    /** 爆发次数，0表示无限 */
    cycles: number;
    /** 当前周期 */
    currentCycle: number;
    /** 上次爆发时间 */
    lastTime: number;
  } | null = null;

  /** 是否活跃 */
  private active: boolean = false;

  /** 已发射时间 */
  private elapsedTime: number = 0;

  /** 上次发射时间 */
  private lastEmitTime: number = 0;

  /** 活跃粒子列表 */
  private particles: Particle[] = [];

  /** 粒子系统引用 */
  private particleSystem: ParticleSystem | null = null;

  /** 所属实体 */
  protected entity: Entity | null = null;

  /** 世界变换矩阵 */
  private worldMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /** 临时四元数 */
  private tempQuaternion: THREE.Quaternion = new THREE.Quaternion();

  /**
   * 创建粒子发射器
   * @param options 发射器选项
   */
  constructor(options: ParticleEmitterOptions = {}) {
    super(ParticleEmitter.type);

    this.name = options.name || '粒子发射器';
    this.shapeType = options.shapeType || EmitterShapeType.POINT;
    this.shapeParams = options.shapeParams || {};

    // 设置纹理
    if (options.texture) {
      if (typeof options.texture === 'string') {
        // 加载纹理
        this.texture = new THREE.TextureLoader().load(options.texture);
      } else {
        this.texture = options.texture;
      }
    }

    // 设置材质
    this.material = options.material || null;

    // 设置粒子属性
    this.setParticleSize(options.particleSize || 1);
    this.setParticleColor(options.particleColor || new THREE.Color(1, 1, 1));
    this.setParticleOpacity(options.particleOpacity || [1, 0]);
    this.setParticleLifetime(options.particleLifetime || 1);
    this.setParticleVelocity(options.particleVelocity || 1);

    if (options.particleAcceleration) {
      this.particleAcceleration.copy(options.particleAcceleration);
    }

    this.setParticleRotationSpeed(options.particleRotationSpeed || 0);
    this.setParticleScaleSpeed(options.particleScaleSpeed || new THREE.Vector2(0, 0));

    // 设置发射器属性
    this.emissionRate = options.emissionRate || 10;
    this.setEmissionAngle(options.emissionAngle || [0, Math.PI * 2]);
    this.setEmissionForce(options.emissionForce || 1);

    if (options.emissionDirection) {
      this.emissionDirection.copy(options.emissionDirection).normalize();
    }

    this.emissionSpread = options.emissionSpread || 0;

    if (options.gravity) {
      this.gravity.copy(options.gravity);
    }

    this.drag = options.drag || 0;
    this.enableCollision = options.enableCollision || false;
    this.enableSorting = options.enableSorting || false;
    this.autoStart = options.autoStart !== undefined ? options.autoStart : true;
    this.duration = options.duration || 0;
    this.maxParticles = options.maxParticles || 1000;
    this.loop = options.loop || false;

    // 设置爆发模式
    if (options.burst) {
      this.burst = {
        count: options.burst.count,
        interval: options.burst.interval,
        cycles: options.burst.cycles,
        currentCycle: 0,
        lastTime: 0
      };
    }
  }

  /**
   * 初始化发射器
   * @param particleSystem 粒子系统
   */
  public initialize(particleSystem: ParticleSystem): void {
    this.particleSystem = particleSystem;

    // 如果设置了自动开始，则启动发射器
    if (this.autoStart) {
      this.start();
    }
  }

  /**
   * 设置所属实体
   * @param entity 实体
   */
  public setEntity(entity: Entity): void {
    this.entity = entity;
  }

  /**
   * 获取所属实体
   * @returns 实体
   */
  public getEntity(): Entity | null {
    return this.entity;
  }

  /**
   * 设置粒子大小
   * @param size 粒子大小或范围
   */
  public setParticleSize(size: number | [number, number]): void {
    if (typeof size === 'number') {
      this.particleSize = [size, size];
    } else {
      this.particleSize = size;
    }
  }

  /**
   * 设置粒子颜色
   * @param color 粒子颜色或范围
   */
  public setParticleColor(color: THREE.Color | [THREE.Color, THREE.Color]): void {
    if (color instanceof THREE.Color) {
      this.particleColor = [color.clone(), color.clone()];
    } else {
      this.particleColor = [color[0].clone(), color[1].clone()];
    }
  }

  /**
   * 设置粒子透明度
   * @param opacity 粒子透明度或范围
   */
  public setParticleOpacity(opacity: number | [number, number]): void {
    if (typeof opacity === 'number') {
      this.particleOpacity = [opacity, opacity];
    } else {
      this.particleOpacity = opacity;
    }
  }

  /**
   * 设置粒子生命周期
   * @param lifetime 粒子生命周期或范围（秒）
   */
  public setParticleLifetime(lifetime: number | [number, number]): void {
    if (typeof lifetime === 'number') {
      this.particleLifetime = [lifetime, lifetime];
    } else {
      this.particleLifetime = lifetime;
    }
  }

  /**
   * 设置粒子速度
   * @param velocity 粒子速度或范围
   */
  public setParticleVelocity(velocity: number | [number, number]): void {
    if (typeof velocity === 'number') {
      this.particleVelocity = [velocity, velocity];
    } else {
      this.particleVelocity = velocity;
    }
  }

  /**
   * 设置粒子旋转速度
   * @param rotationSpeed 粒子旋转速度或范围
   */
  public setParticleRotationSpeed(rotationSpeed: number | [number, number]): void {
    if (typeof rotationSpeed === 'number') {
      this.particleRotationSpeed = [rotationSpeed, rotationSpeed];
    } else {
      this.particleRotationSpeed = rotationSpeed;
    }
  }

  /**
   * 设置粒子缩放速度
   * @param scaleSpeed 粒子缩放速度或范围
   */
  public setParticleScaleSpeed(scaleSpeed: THREE.Vector2 | [THREE.Vector2, THREE.Vector2]): void {
    if (scaleSpeed instanceof THREE.Vector2) {
      this.particleScaleSpeed = [scaleSpeed.clone(), scaleSpeed.clone()];
    } else {
      this.particleScaleSpeed = [scaleSpeed[0].clone(), scaleSpeed[1].clone()];
    }
  }

  /**
   * 设置发射角度
   * @param angle 发射角度或范围
   */
  public setEmissionAngle(angle: number | [number, number]): void {
    if (typeof angle === 'number') {
      this.emissionAngle = [angle, angle];
    } else {
      this.emissionAngle = angle;
    }
  }

  /**
   * 设置发射力度
   * @param force 发射力度或范围
   */
  public setEmissionForce(force: number | [number, number]): void {
    if (typeof force === 'number') {
      this.emissionForce = [force, force];
    } else {
      this.emissionForce = force;
    }
  }

  /**
   * 开始发射粒子
   */
  public start(): void {
    if (!this.active) {
      this.active = true;
      this.elapsedTime = 0;
      this.lastEmitTime = 0;

      if (this.burst) {
        this.burst.currentCycle = 0;
        this.burst.lastTime = 0;
      }
    }
  }

  /**
   * 停止发射粒子
   */
  public stop(): void {
    this.active = false;
  }

  /**
   * 清除所有粒子
   */
  public clearParticles(): void {
    // 释放所有活跃粒子
    for (const particle of this.particles) {
      if (this.particleSystem) {
        this.particleSystem.releaseParticle(particle);
      }
    }

    this.particles = [];
  }

  /**
   * 重置发射器
   */
  public reset(): void {
    this.stop();
    this.clearParticles();
    this.elapsedTime = 0;
    this.lastEmitTime = 0;

    if (this.burst) {
      this.burst.currentCycle = 0;
      this.burst.lastTime = 0;
    }
  }

  /**
   * 更新发射器
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新发射器时间
    this.elapsedTime += deltaTime;

    // 检查发射持续时间
    if (this.duration > 0 && this.elapsedTime >= this.duration) {
      if (this.loop) {
        // 循环模式，重置时间
        this.elapsedTime = 0;
      } else {
        // 非循环模式，停止发射
        this.stop();
      }
    }

    // 更新世界矩阵
    this.updateWorldMatrix();

    // 如果活跃且有粒子系统，发射新粒子
    if (this.active && this.particleSystem) {
      // 计算本帧应该发射的粒子数量
      let emitCount = 0;

      if (this.burst) {
        // 爆发模式
        if (this.burst.cycles === 0 || this.burst.currentCycle < this.burst.cycles) {
          if (this.elapsedTime - this.burst.lastTime >= this.burst.interval) {
            emitCount = this.burst.count;
            this.burst.lastTime = this.elapsedTime;
            this.burst.currentCycle++;
          }
        }
      } else {
        // 连续发射模式
        const emitInterval = 1 / this.emissionRate;
        const timeSinceLastEmit = this.elapsedTime - this.lastEmitTime;

        if (timeSinceLastEmit >= emitInterval) {
          emitCount = Math.floor(timeSinceLastEmit / emitInterval);
          this.lastEmitTime = this.elapsedTime;
        }
      }

      // 发射粒子
      for (let i = 0; i < emitCount; i++) {
        if (this.particles.length < this.maxParticles) {
          this.emitParticle();
        }
      }
    }

    // 更新所有活跃粒子
    this.updateParticles(deltaTime);
  }

  /**
   * 更新世界矩阵
   */
  private updateWorldMatrix(): void {
    if (this.entity) {
      // 获取实体的世界变换
      const transform = this.entity.getComponent<Transform>('Transform');
      if (transform) {
        this.worldMatrix.copy(transform.getWorldMatrix());
      } else {
        this.worldMatrix.identity();
      }
    } else {
      this.worldMatrix.identity();
    }
  }

  /**
   * 发射单个粒子
   */
  private emitParticle(): void {
    if (!this.particleSystem) return;

    // 从粒子系统创建新粒子
    const particle = this.particleSystem.createParticle();
    if (!particle) return;

    // 设置粒子初始属性
    this.initializeParticle(particle);

    // 添加到活跃粒子列表
    this.particles.push(particle);
  }

  /**
   * 初始化粒子属性
   * @param particle 粒子
   */
  private initializeParticle(particle: Particle): void {
    // 设置粒子位置
    this.setParticlePosition(particle);

    // 设置粒子速度
    this.setParticleVelocityVector(particle);

    // 设置粒子生命周期
    particle.lifetime = this.randomRange(this.particleLifetime[0], this.particleLifetime[1]);

    // 设置粒子大小
    const size = this.randomRange(this.particleSize[0], this.particleSize[1]);
    particle.scale.set(size, size);

    // 设置粒子颜色
    particle.startColor.copy(this.particleColor[0]);
    particle.endColor.copy(this.particleColor[1]);
    particle.color.copy(particle.startColor);

    // 设置粒子透明度
    particle.startOpacity = this.particleOpacity[0];
    particle.endOpacity = this.particleOpacity[1];
    particle.opacity = particle.startOpacity;

    // 设置粒子旋转
    particle.rotation = Math.random() * Math.PI * 2;
    particle.rotationSpeed = this.randomRange(this.particleRotationSpeed[0], this.particleRotationSpeed[1]);

    // 设置粒子缩放速度
    const scaleSpeedX = this.randomRange(this.particleScaleSpeed[0].x, this.particleScaleSpeed[1].x);
    const scaleSpeedY = this.randomRange(this.particleScaleSpeed[0].y, this.particleScaleSpeed[1].y);
    particle.scaleSpeed.set(scaleSpeedX, scaleSpeedY);

    // 设置粒子物理属性
    particle.drag = this.drag;
    particle.gravityScale = 1;

    // 重置粒子年龄
    particle.age = 0;
    particle.normalizedAge = 0;
  }

  /**
   * 设置粒子初始位置
   * @param particle 粒子
   */
  private setParticlePosition(particle: Particle): void {
    // 根据发射器形状设置粒子位置
    switch (this.shapeType) {
      case EmitterShapeType.POINT:
        // 点发射器，位置就是发射器位置
        particle.position.set(0, 0, 0);
        break;

      case EmitterShapeType.SPHERE:
        // 球形发射器
        const radius = this.shapeParams.radius || 1;
        const phi = Math.random() * Math.PI * 2;
        const theta = Math.random() * Math.PI;

        particle.position.set(
          radius * Math.sin(theta) * Math.cos(phi),
          radius * Math.sin(theta) * Math.sin(phi),
          radius * Math.cos(theta)
        );
        break;

      case EmitterShapeType.CIRCLE:
        // 圆形发射器
        const circleRadius = this.shapeParams.radius || 1;
        const angle = Math.random() * Math.PI * 2;

        particle.position.set(
          circleRadius * Math.cos(angle),
          circleRadius * Math.sin(angle),
          0
        );
        break;

      case EmitterShapeType.RECTANGLE:
        // 矩形发射器
        const width = this.shapeParams.width || 1;
        const height = this.shapeParams.height || 1;

        particle.position.set(
          (Math.random() - 0.5) * width,
          (Math.random() - 0.5) * height,
          0
        );
        break;

      case EmitterShapeType.CONE:
        // 圆锥发射器
        const coneRadius = this.shapeParams.radius || 1;
        const coneAngle = Math.random() * Math.PI * 2;
        const coneHeight = this.shapeParams.height || 1;

        particle.position.set(
          coneRadius * Math.cos(coneAngle),
          coneRadius * Math.sin(coneAngle),
          coneHeight
        );
        break;

      case EmitterShapeType.TORUS:
        // 圆环发射器
        const torusRadius = this.shapeParams.radius || 1;
        const tubeRadius = this.shapeParams.tubeRadius || 0.1;
        const u = Math.random() * Math.PI * 2;
        const v = Math.random() * Math.PI * 2;

        particle.position.set(
          (torusRadius + tubeRadius * Math.cos(v)) * Math.cos(u),
          (torusRadius + tubeRadius * Math.cos(v)) * Math.sin(u),
          tubeRadius * Math.sin(v)
        );
        break;

      case EmitterShapeType.MESH:
        // 网格发射器
        // 需要实现从网格表面采样点的逻辑
        particle.position.set(0, 0, 0);
        break;

      default:
        particle.position.set(0, 0, 0);
        break;
    }

    // 应用世界变换
    particle.position.applyMatrix4(this.worldMatrix);
    particle.previousPosition.copy(particle.position);
  }

  /**
   * 设置粒子初始速度向量
   * @param particle 粒子
   */
  private setParticleVelocityVector(particle: Particle): void {
    // 计算发射方向
    let direction = new THREE.Vector3();

    if (this.emissionSpread <= 0) {
      // 无扩散，使用固定方向
      direction.copy(this.emissionDirection);
    } else {
      // 有扩散，在锥体内随机方向
      const angle = this.randomRange(this.emissionAngle[0], this.emissionAngle[1]);
      const spread = this.emissionSpread * Math.random();

      // 创建一个随机旋转四元数
      this.tempQuaternion.setFromAxisAngle(
        new THREE.Vector3(0, 0, 1),
        angle
      );

      // 应用旋转到方向向量
      direction.copy(this.emissionDirection).applyQuaternion(this.tempQuaternion);

      // 应用扩散
      if (spread > 0) {
        const perpendicular = new THREE.Vector3().crossVectors(
          direction,
          direction.y !== 0 || direction.z !== 0
            ? new THREE.Vector3(1, 0, 0)
            : new THREE.Vector3(0, 1, 0)
        ).normalize();

        const perpendicular2 = new THREE.Vector3().crossVectors(
          direction,
          perpendicular
        ).normalize();

        const spreadAngle = spread * Math.PI;
        const randomAngle = Math.random() * Math.PI * 2;

        perpendicular.multiplyScalar(Math.sin(spreadAngle) * Math.cos(randomAngle));
        perpendicular2.multiplyScalar(Math.sin(spreadAngle) * Math.sin(randomAngle));

        direction.multiplyScalar(Math.cos(spreadAngle))
          .add(perpendicular)
          .add(perpendicular2)
          .normalize();
      }
    }

    // 应用发射力度
    const force = this.randomRange(this.emissionForce[0], this.emissionForce[1]);
    const velocity = this.randomRange(this.particleVelocity[0], this.particleVelocity[1]);

    // 设置粒子速度
    particle.velocity.copy(direction).multiplyScalar(force * velocity);

    // 应用世界旋转
    const worldRotation = new THREE.Quaternion().setFromRotationMatrix(this.worldMatrix);
    particle.velocity.applyQuaternion(worldRotation);
  }

  /**
   * 更新所有活跃粒子
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateParticles(deltaTime: number): void {
    // 遍历所有活跃粒子
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];

      // 应用重力
      if (particle.gravityScale !== 0) {
        particle.applyGravity(this.gravity);
      }

      // 更新粒子
      const isActive = particle.update(deltaTime);

      // 如果粒子不再活跃，从列表中移除
      if (!isActive) {
        if (this.particleSystem) {
          this.particleSystem.releaseParticle(particle);
        }
        this.particles.splice(i, 1);
      }
    }
  }

  /**
   * 对粒子进行排序
   * @param cameraPosition 相机位置
   */
  public sortParticles(cameraPosition: THREE.Vector3): void {
    if (!this.enableSorting || this.particles.length <= 1) {
      return;
    }

    // 按照到相机的距离排序
    this.particles.sort((a, b) => {
      const distA = a.position.distanceToSquared(cameraPosition);
      const distB = b.position.distanceToSquared(cameraPosition);
      return distB - distA; // 从远到近排序
    });
  }

  /**
   * 获取活跃粒子数量
   * @returns 活跃粒子数量
   */
  public getActiveParticleCount(): number {
    return this.particles.length;
  }

  /**
   * 获取所有活跃粒子
   * @returns 活跃粒子数组
   */
  public getParticles(): Particle[] {
    return [...this.particles];
  }

  /**
   * 检查发射器是否活跃
   * @returns 是否活跃
   */
  public isActive(): boolean {
    return this.active;
  }

  /**
   * 生成指定范围内的随机数
   * @param min 最小值
   * @param max 最大值
   * @returns 随机数
   */
  private randomRange(min: number, max: number): number {
    return min + Math.random() * (max - min);
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new ParticleEmitter({
      name: this.name,
      shapeType: this.shapeType,
      shapeParams: { ...this.shapeParams },
      texture: this.texture,
      material: this.material,
      particleSize: [...this.particleSize] as [number, number],
      particleColor: [this.particleColor[0].clone(), this.particleColor[1].clone()] as [THREE.Color, THREE.Color],
      particleOpacity: [...this.particleOpacity] as [number, number],
      particleLifetime: [...this.particleLifetime] as [number, number],
      particleVelocity: [...this.particleVelocity] as [number, number],
      particleRotationSpeed: [...this.particleRotationSpeed] as [number, number],
      particleScaleSpeed: [this.particleScaleSpeed[0].clone(), this.particleScaleSpeed[1].clone()] as [THREE.Vector2, THREE.Vector2],
      emissionRate: this.emissionRate,
      emissionAngle: Array.isArray(this.emissionAngle) ? [...this.emissionAngle] as [number, number] : this.emissionAngle,
      emissionForce: Array.isArray(this.emissionForce) ? [...this.emissionForce] as [number, number] : this.emissionForce,
      emissionDirection: this.emissionDirection.clone(),
      emissionSpread: this.emissionSpread,
      gravity: this.gravity.clone(),
      drag: this.drag,
      enableCollision: this.enableCollision,
      enableSorting: this.enableSorting,
      autoStart: this.autoStart,
      duration: this.duration,
      maxParticles: this.maxParticles,
      loop: this.loop,
      burst: this.burst ? { ...this.burst } : undefined
    });
  }
}