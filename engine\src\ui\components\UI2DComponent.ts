/**
 * UI2DComponent.ts
 *
 * 2D UI元素组件，用于创建和管理2D界面元素
 */

import { Vector2 } from 'three';
import { UIComponent, UIComponentProps, UIComponentType } from './UIComponent';

/**
 * 2D UI元素属性
 */
export interface UI2DComponentProps extends UIComponentProps {
  // 2D特有属性
  htmlElement?: HTMLElement;
  cssClass?: string;
  cssStyle?: Partial<CSSStyleDeclaration>;
  innerHTML?: string;
  textContent?: string;

  // 文本属性
  fontSize?: number | string;
  fontFamily?: string;
  fontWeight?: string | number;
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  textColor?: string;
  lineHeight?: number | string;

  // 事件处理
  onFocus?: (event: any) => void;
  onBlur?: (event: any) => void;
  onKeyDown?: (event: any) => void;
  onKeyUp?: (event: any) => void;
  onChange?: (event: any) => void;
}

/**
 * 2D UI元素组件
 * 用于创建和管理2D界面元素
 */
export class UI2DComponent extends UIComponent {
  // HTML元素 - 继承自基类，不需要重新声明

  // CSS类和样式
  cssClass: string = '';
  cssStyle: Partial<CSSStyleDeclaration> = {};

  // 内容
  innerHTML: string = '';
  textContent: string = '';

  // 文本属性
  fontSize?: number | string;
  fontFamily?: string;
  fontWeight?: string | number;
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  textColor?: string;
  lineHeight?: number | string;

  /**
   * 构造函数
   * @param props 2D UI元素属性
   */
  constructor(props: UI2DComponentProps = {}) {
    super({ ...props, is3D: false });

    // 设置HTML元素
    this.htmlElement = props.htmlElement;

    // 设置CSS类和样式
    this.cssClass = props.cssClass || '';
    this.cssStyle = props.cssStyle || {};

    // 设置内容
    this.innerHTML = props.innerHTML || '';
    this.textContent = props.textContent || '';

    // 设置文本属性
    this.fontSize = props.fontSize;
    this.fontFamily = props.fontFamily;
    this.fontWeight = props.fontWeight;
    this.textAlign = props.textAlign;
    this.textColor = props.textColor;
    this.lineHeight = props.lineHeight;

    // 设置事件处理器
    if (props.onFocus) this.addEventListener('focus', props.onFocus);
    if (props.onBlur) this.addEventListener('blur', props.onBlur);
    if (props.onKeyDown) this.addEventListener('keydown', props.onKeyDown);
    if (props.onKeyUp) this.addEventListener('keyup', props.onKeyUp);
    if (props.onChange) this.addEventListener('change', props.onChange);

    // 创建HTML元素（如果没有提供）
    if (!this.htmlElement) {
      this.createHTMLElement();
    }

    // 应用样式
    this.applyStyles();
  }

  /**
   * 创建HTML元素
   */
  protected createHTMLElement(): void {
    // 根据组件类型创建相应的HTML元素
    switch (this.uiType) {
      case UIComponentType.BUTTON:
        this.htmlElement = document.createElement('button');
        break;
      case UIComponentType.TEXT:
        this.htmlElement = document.createElement('span');
        break;
      case UIComponentType.IMAGE:
        this.htmlElement = document.createElement('img');
        break;
      case UIComponentType.INPUT:
        this.htmlElement = document.createElement('input');
        break;
      case UIComponentType.CHECKBOX:
        this.htmlElement = document.createElement('input');
        (this.htmlElement as HTMLInputElement).type = 'checkbox';
        break;
      case UIComponentType.SLIDER:
        this.htmlElement = document.createElement('input');
        (this.htmlElement as HTMLInputElement).type = 'range';
        break;
      case UIComponentType.DROPDOWN:
        this.htmlElement = document.createElement('select');
        break;
      case UIComponentType.PROGRESS_BAR:
      case UIComponentType.TOOLTIP:
      case UIComponentType.MODAL:
      case UIComponentType.TAB_PANEL:
      case UIComponentType.TREE_VIEW:
      case UIComponentType.LIST_VIEW:
      case UIComponentType.SCROLL_BAR:
      case UIComponentType.MENU:
      case UIComponentType.PANEL:
      case UIComponentType.CONTAINER:
      case UIComponentType.WINDOW:
      default:
        this.htmlElement = document.createElement('div');
        break;
    }

    // 设置ID
    this.htmlElement.id = this.id;

    // 设置内容
    if (this.innerHTML) {
      this.htmlElement.innerHTML = this.innerHTML;
    } else if (this.textContent) {
      this.htmlElement.textContent = this.textContent;
    }

    // 添加事件监听器
    this.addHTMLEventListeners();
  }

  /**
   * 应用样式
   */
  private applyStyles(): void {
    if (!this.htmlElement) return;

    // 应用CSS类
    if (this.cssClass) {
      this.htmlElement.className = this.cssClass;
    }

    // 应用基本样式
    const style = this.htmlElement.style;

    // 位置和尺寸
    style.position = 'absolute';
    style.left = this.position instanceof Vector2 ? `${this.position.x}px` : `${(this.position as any).x}px`;
    style.top = this.position instanceof Vector2 ? `${this.position.y}px` : `${(this.position as any).y}px`;
    style.width = `${this.size.x}px`;
    style.height = `${this.size.y}px`;

    // 可见性和透明度
    style.visibility = this.visible ? 'visible' : 'hidden';
    style.opacity = this.opacity.toString();
    style.zIndex = this.zIndex.toString();

    // 背景和边框
    if (this.backgroundColor) style.backgroundColor = this.backgroundColor;
    if (this.borderColor) style.borderColor = this.borderColor;
    if (this.borderWidth > 0) style.borderWidth = `${this.borderWidth}px`;
    if (this.borderRadius > 0) style.borderRadius = `${this.borderRadius}px`;

    // 内边距和外边距
    style.paddingTop = `${this.padding.top}px`;
    style.paddingRight = `${this.padding.right}px`;
    style.paddingBottom = `${this.padding.bottom}px`;
    style.paddingLeft = `${this.padding.left}px`;

    style.marginTop = `${this.margin.top}px`;
    style.marginRight = `${this.margin.right}px`;
    style.marginBottom = `${this.margin.bottom}px`;
    style.marginLeft = `${this.margin.left}px`;

    // 文本样式
    if (this.fontSize) style.fontSize = typeof this.fontSize === 'number' ? `${this.fontSize}px` : this.fontSize;
    if (this.fontFamily) style.fontFamily = this.fontFamily;
    if (this.fontWeight) style.fontWeight = this.fontWeight.toString();
    if (this.textAlign) style.textAlign = this.textAlign;
    if (this.textColor) style.color = this.textColor;
    if (this.lineHeight) style.lineHeight = typeof this.lineHeight === 'number' ? `${this.lineHeight}px` : this.lineHeight;

    // 交互性
    style.pointerEvents = this.interactive ? 'auto' : 'none';

    // 应用自定义CSS样式
    for (const key in this.cssStyle) {
      style[key as any] = this.cssStyle[key] as string;
    }
  }

  /**
   * 添加HTML事件监听器
   */
  private addHTMLEventListeners(): void {
    if (!this.htmlElement) return;

    // 点击事件
    this.htmlElement.addEventListener('click', (event) => {
      this.triggerEvent('click', event);
    });

    // 悬停事件
    this.htmlElement.addEventListener('mouseenter', (event) => {
      this.triggerEvent('hover', { type: 'enter', event });
    });

    this.htmlElement.addEventListener('mouseleave', (event) => {
      this.triggerEvent('hover', { type: 'leave', event });
    });

    // 拖拽事件
    this.htmlElement.addEventListener('dragstart', (event) => {
      this.triggerEvent('dragstart', event);
    });

    this.htmlElement.addEventListener('drag', (event) => {
      this.triggerEvent('drag', event);
    });

    this.htmlElement.addEventListener('dragend', (event) => {
      this.triggerEvent('dragend', event);
    });

    // 焦点事件
    this.htmlElement.addEventListener('focus', (event) => {
      this.triggerEvent('focus', event);
    });

    this.htmlElement.addEventListener('blur', (event) => {
      this.triggerEvent('blur', event);
    });

    // 键盘事件
    this.htmlElement.addEventListener('keydown', (event) => {
      this.triggerEvent('keydown', event);
    });

    this.htmlElement.addEventListener('keyup', (event) => {
      this.triggerEvent('keyup', event);
    });

    // 变更事件（针对输入元素）
    if (this.uiType === UIComponentType.INPUT || this.uiType === UIComponentType.CHECKBOX ||
        this.uiType === UIComponentType.SLIDER || this.uiType === UIComponentType.DROPDOWN) {
      this.htmlElement.addEventListener('change', (event) => {
        this.triggerEvent('change', event);
      });
    }
  }

  /**
   * 更新UI元素
   * @param deltaTime 时间增量
   */
  override update(deltaTime: number): void {
    super.update(deltaTime);

    // 更新HTML元素样式
    this.applyStyles();
  }

  /**
   * 渲染UI元素
   */
  override render(): void {
    super.render();

    // 2D UI元素不需要额外的渲染逻辑，因为它们由浏览器渲染
  }

  /**
   * 销毁UI元素
   */
  override dispose(): void {
    super.dispose();

    // 移除HTML元素
    if (this.htmlElement && this.htmlElement.parentNode) {
      this.htmlElement.parentNode.removeChild(this.htmlElement);
    }

    this.htmlElement = undefined;
  }

  /**
   * 设置HTML内容
   * @param html HTML内容
   */
  setHTML(html: string): void {
    this.innerHTML = html;
    if (this.htmlElement) {
      this.htmlElement.innerHTML = html;
    }
  }

  /**
   * 设置文本内容
   * @param text 文本内容
   */
  setText(text: string): void {
    this.textContent = text;
    if (this.htmlElement) {
      this.htmlElement.textContent = text;
    }
  }

  /**
   * 设置CSS类
   * @param cssClass CSS类名
   */
  setCSSClass(cssClass: string): void {
    this.cssClass = cssClass;
    if (this.htmlElement) {
      this.htmlElement.className = cssClass;
    }
  }

  /**
   * 添加CSS类
   * @param cssClass CSS类名
   */
  addCSSClass(cssClass: string): void {
    if (this.htmlElement) {
      this.htmlElement.classList.add(cssClass);
    }
  }

  /**
   * 移除CSS类
   * @param cssClass CSS类名
   */
  removeCSSClass(cssClass: string): void {
    if (this.htmlElement) {
      this.htmlElement.classList.remove(cssClass);
    }
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new UI2DComponent({
      id: this.id,
      type: this.uiType,
      visible: this.visible,
      interactive: this.interactive,
      position: this.position instanceof Vector2 ? new Vector2(this.position.x, this.position.y) : this.position.clone(),
      size: new Vector2(this.size.x, this.size.y),
      opacity: this.opacity,
      zIndex: this.zIndex,
      layoutType: this.layoutType,
      layoutParams: { ...this.layoutParams },
      backgroundColor: this.backgroundColor,
      borderColor: this.borderColor,
      borderWidth: this.borderWidth,
      borderRadius: this.borderRadius,
      padding: { ...this.padding },
      margin: { ...this.margin },
      data: { ...this.data },
      tags: [...this.tags],
      is3D: this.is3D,
      cssClass: this.cssClass,
      cssStyle: this.cssStyle ? { ...this.cssStyle } : undefined,
      innerHTML: this.innerHTML,
      textContent: this.textContent,
      fontSize: this.fontSize,
      fontFamily: this.fontFamily,
      fontWeight: this.fontWeight,
      textAlign: this.textAlign,
      textColor: this.textColor,
      lineHeight: this.lineHeight
    });
  }
}
