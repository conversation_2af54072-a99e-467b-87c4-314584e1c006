/**
 * UIListViewComponent.ts
 * 
 * 列表控件UI组件，用于显示数据列表
 */

import { UIComponent, UIComponentType } from './UIComponent';
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { Vector2, Vector3 } from 'three';

/**
 * 列表项接口
 */
export interface ListItem {
  /** 项目ID */
  id: string;
  /** 项目标题 */
  title: string;
  /** 项目副标题 */
  subtitle?: string;
  /** 项目描述 */
  description?: string;
  /** 项目图标 */
  icon?: string;
  /** 项目图片 */
  image?: string;
  /** 项目数据 */
  data?: any;
  /** 是否选中 */
  selected?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否可拖拽 */
  draggable?: boolean;
  /** 项目类型 */
  type?: string;
  /** 自定义样式类 */
  className?: string;
  /** 工具提示 */
  tooltip?: string;
  /** 标签 */
  tags?: string[];
  /** 状态 */
  status?: 'normal' | 'warning' | 'error' | 'success' | 'info';
  /** 自定义高度 */
  height?: number;
}

/**
 * 列表视图模式
 */
export enum ListViewMode {
  LIST = 'list',
  GRID = 'grid',
  CARD = 'card',
  TABLE = 'table'
}

/**
 * 列表排序配置
 */
export interface ListSortConfig {
  /** 排序字段 */
  field: string;
  /** 排序方向 */
  direction: 'asc' | 'desc';
  /** 自定义排序函数 */
  compareFn?: (a: ListItem, b: ListItem) => number;
}

/**
 * 列表过滤配置
 */
export interface ListFilterConfig {
  /** 过滤字段 */
  field: string;
  /** 过滤值 */
  value: any;
  /** 过滤操作符 */
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte';
  /** 自定义过滤函数 */
  filterFn?: (item: ListItem, value: any) => boolean;
}

/**
 * 列表控件配置
 */
export interface ListViewConfig {
  /** 视图模式 */
  mode: ListViewMode;
  /** 是否支持多选 */
  multiSelect: boolean;
  /** 是否支持拖拽 */
  draggable: boolean;
  /** 是否支持搜索 */
  searchable: boolean;
  /** 是否支持排序 */
  sortable: boolean;
  /** 是否支持过滤 */
  filterable: boolean;
  /** 是否虚拟化 */
  virtualized: boolean;
  /** 项目高度 */
  itemHeight: number;
  /** 网格列数（网格模式） */
  gridColumns: number;
  /** 项目间距 */
  itemSpacing: number;
  /** 是否显示索引 */
  showIndex: boolean;
  /** 是否显示复选框 */
  showCheckbox: boolean;
  /** 是否显示分组 */
  showGroups: boolean;
  /** 分页大小 */
  pageSize: number;
  /** 是否启用分页 */
  pagination: boolean;
  /** 加载更多阈值 */
  loadMoreThreshold: number;
}

/**
 * 列表控件事件
 */
export interface ListViewEvents {
  onItemSelect?: (item: ListItem, selected: boolean) => void;
  onItemClick?: (item: ListItem, event: MouseEvent) => void;
  onItemDoubleClick?: (item: ListItem, event: MouseEvent) => void;
  onItemContextMenu?: (item: ListItem, event: MouseEvent) => void;
  onItemDragStart?: (item: ListItem, event: DragEvent) => void;
  onItemDragOver?: (item: ListItem, event: DragEvent) => void;
  onItemDrop?: (dragItem: ListItem, dropItem: ListItem, position: 'before' | 'after') => void;
  onSearch?: (keyword: string, matchedItems: ListItem[]) => void;
  onSort?: (sortConfig: ListSortConfig) => void;
  onFilter?: (filterConfigs: ListFilterConfig[]) => void;
  onLoadMore?: () => void;
  onPageChange?: (page: number, pageSize: number) => void;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: ListViewConfig = {
  mode: ListViewMode.LIST,
  multiSelect: false,
  draggable: false,
  searchable: false,
  sortable: false,
  filterable: false,
  virtualized: false,
  itemHeight: 48,
  gridColumns: 3,
  itemSpacing: 8,
  showIndex: false,
  showCheckbox: false,
  showGroups: false,
  pageSize: 50,
  pagination: false,
  loadMoreThreshold: 10
};

/**
 * 列表控件组件
 */
export class UIListViewComponent extends UIComponent {
  // htmlElement已在基类中定义，无需重复声明
  
  private items: ListItem[] = [];
  private originalItems: ListItem[] = [];
  private config: ListViewConfig;
  private events: ListViewEvents;
  private selectedItems: Set<string> = new Set();
  private sortedItems: ListItem[] = [];
  private searchKeyword: string = '';
  private sortConfig?: ListSortConfig;
  private filterConfigs: ListFilterConfig[] = [];
  private containerElement?: HTMLElement;
  private currentPage: number = 1;
  private totalPages: number = 1;
  private visibleRange: { start: number; end: number } = { start: 0, end: 0 };
  private isLoading: boolean = false;

  constructor(entity: Entity, config: Partial<ListViewConfig> = {}, events: ListViewEvents = {}) {
    super({ type: UIComponentType.LIST_VIEW });
    this.entity = entity;
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.events = events;
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    this.createHtmlElement();
    this.createListElement();
    this.updateFilteredItems();
    this.render();
  }

  /**
   * 创建HTML元素
   */
  private createHtmlElement(): void {
    if (!this.htmlElement) {
      this.htmlElement = document.createElement('div');
      this.htmlElement.className = 'ui-list-view-container';
      this.htmlElement.style.cssText = `
        position: relative;
        width: 100%;
        height: 100%;
      `;
    }
  }

  /**
   * 设置列表项数据
   */
  public setItems(items: ListItem[]): void {
    this.items = items;
    this.originalItems = [...items];
    this.updateFilteredItems();
    this.render();
  }

  /**
   * 获取列表项数据
   */
  public getItems(): ListItem[] {
    return this.items;
  }

  /**
   * 添加项目
   */
  public addItem(item: ListItem, index?: number): void {
    if (index !== undefined) {
      this.items.splice(index, 0, item);
      this.originalItems.splice(index, 0, item);
    } else {
      this.items.push(item);
      this.originalItems.push(item);
    }
    
    this.updateFilteredItems();
    this.render();
  }

  /**
   * 移除项目
   */
  public removeItem(itemId: string): void {
    this.items = this.items.filter(item => item.id !== itemId);
    this.originalItems = this.originalItems.filter(item => item.id !== itemId);
    this.selectedItems.delete(itemId);
    
    this.updateFilteredItems();
    this.render();
  }

  /**
   * 更新项目
   */
  public updateItem(itemId: string, updates: Partial<ListItem>): void {
    const itemIndex = this.items.findIndex(item => item.id === itemId);
    const originalIndex = this.originalItems.findIndex(item => item.id === itemId);
    
    if (itemIndex !== -1) {
      Object.assign(this.items[itemIndex], updates);
    }
    if (originalIndex !== -1) {
      Object.assign(this.originalItems[originalIndex], updates);
    }
    
    this.updateFilteredItems();
    this.render();
  }

  /**
   * 查找项目
   */
  public findItem(itemId: string): ListItem | null {
    return this.items.find(item => item.id === itemId) || null;
  }

  /**
   * 选择项目
   */
  public selectItem(itemId: string, selected: boolean = true): void {
    const item = this.findItem(itemId);
    if (!item || item.disabled) return;

    if (selected) {
      if (!this.config.multiSelect) {
        this.selectedItems.clear();
        this.items.forEach(item => item.selected = false);
      }
      this.selectedItems.add(itemId);
      item.selected = true;
    } else {
      this.selectedItems.delete(itemId);
      item.selected = false;
    }

    this.events.onItemSelect?.(item, selected);
    this.render();
  }

  /**
   * 获取选中的项目
   */
  public getSelectedItems(): ListItem[] {
    return Array.from(this.selectedItems).map(id => this.findItem(id)!).filter(Boolean);
  }

  /**
   * 清除选择
   */
  public clearSelection(): void {
    this.selectedItems.clear();
    this.items.forEach(item => item.selected = false);
    this.render();
  }

  /**
   * 搜索项目
   */
  public search(keyword: string): void {
    this.searchKeyword = keyword.toLowerCase();
    this.updateFilteredItems();
    this.render();
  }

  /**
   * 排序项目
   */
  public sort(sortConfig: ListSortConfig): void {
    this.sortConfig = sortConfig;
    this.updateFilteredItems();
    this.events.onSort?.(sortConfig);
    this.render();
  }

  /**
   * 过滤项目
   */
  public filter(filterConfigs: ListFilterConfig[]): void {
    this.filterConfigs = filterConfigs;
    this.updateFilteredItems();
    this.events.onFilter?.(filterConfigs);
    this.render();
  }

  /**
   * 清除过滤
   */
  public clearFilters(): void {
    this.filterConfigs = [];
    this.updateFilteredItems();
    this.render();
  }

  /**
   * 设置页码
   */
  public setPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.events.onPageChange?.(page, this.config.pageSize);
      this.render();
    }
  }

  /**
   * 滚动到项目
   */
  public scrollToItem(itemId: string): void {
    const index = this.sortedItems.findIndex(item => item.id === itemId);
    if (index !== -1 && this.containerElement) {
      const scrollTop = index * this.config.itemHeight;
      this.containerElement.scrollTop = scrollTop;
    }
  }

  /**
   * 更新过滤后的项目
   */
  private updateFilteredItems(): void {
    let filtered = [...this.originalItems];

    // 应用搜索
    if (this.searchKeyword) {
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(this.searchKeyword) ||
        (item.subtitle && item.subtitle.toLowerCase().includes(this.searchKeyword)) ||
        (item.description && item.description.toLowerCase().includes(this.searchKeyword))
      );
      
      this.events.onSearch?.(this.searchKeyword, filtered);
    }

    // 应用过滤器
    this.filterConfigs.forEach(filterConfig => {
      filtered = filtered.filter(item => {
        if (filterConfig.filterFn) {
          return filterConfig.filterFn(item, filterConfig.value);
        }
        
        const fieldValue = (item as any)[filterConfig.field];
        
        switch (filterConfig.operator) {
          case 'equals':
            return fieldValue === filterConfig.value;
          case 'contains':
            return String(fieldValue).toLowerCase().includes(String(filterConfig.value).toLowerCase());
          case 'startsWith':
            return String(fieldValue).toLowerCase().startsWith(String(filterConfig.value).toLowerCase());
          case 'endsWith':
            return String(fieldValue).toLowerCase().endsWith(String(filterConfig.value).toLowerCase());
          case 'gt':
            return fieldValue > filterConfig.value;
          case 'lt':
            return fieldValue < filterConfig.value;
          case 'gte':
            return fieldValue >= filterConfig.value;
          case 'lte':
            return fieldValue <= filterConfig.value;
          default:
            return true;
        }
      });
    });

    // 应用排序
    if (this.sortConfig) {
      filtered.sort((a, b) => {
        if (this.sortConfig!.compareFn) {
          return this.sortConfig!.compareFn(a, b);
        }
        
        const aValue = (a as any)[this.sortConfig!.field];
        const bValue = (b as any)[this.sortConfig!.field];
        
        let result = 0;
        if (aValue < bValue) result = -1;
        else if (aValue > bValue) result = 1;
        
        return this.sortConfig!.direction === 'desc' ? -result : result;
      });
    }

    this.items = filtered;
    
    // 更新分页
    if (this.config.pagination) {
      this.totalPages = Math.ceil(filtered.length / this.config.pageSize);
      const startIndex = (this.currentPage - 1) * this.config.pageSize;
      const endIndex = startIndex + this.config.pageSize;
      this.sortedItems = filtered.slice(startIndex, endIndex);
    } else {
      this.sortedItems = filtered;
    }

    // 更新可见范围
    if (this.config.virtualized && this.containerElement) {
      this.updateVisibleRange();
    }
  }

  /**
   * 更新可见范围（虚拟化）
   */
  private updateVisibleRange(): void {
    if (!this.containerElement) return;

    const containerHeight = this.containerElement.clientHeight;
    const scrollTop = this.containerElement.scrollTop;
    const itemHeight = this.config.itemHeight;
    
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      this.sortedItems.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight)
    );
    
    this.visibleRange = { start: startIndex, end: endIndex };
    
    // 检查是否需要加载更多
    if (!this.config.pagination && 
        endIndex >= this.sortedItems.length - this.config.loadMoreThreshold &&
        !this.isLoading) {
      this.isLoading = true;
      this.events.onLoadMore?.();
    }
  }

  /**
   * 创建列表元素
   */
  private createListElement(): void {
    if (!this.htmlElement) return;

    this.containerElement = document.createElement('div');
    this.containerElement.className = `ui-list-view mode-${this.config.mode}`;
    this.containerElement.style.cssText = `
      width: 100%;
      height: 100%;
      overflow: auto;
      font-family: var(--ui-font-primary);
      font-size: var(--ui-font-size-md);
      color: var(--ui-color-text);
      background: var(--ui-color-background);
      border: 1px solid var(--ui-color-border);
    `;

    if (this.config.virtualized) {
      this.containerElement.addEventListener('scroll', () => {
        this.updateVisibleRange();
        this.render();
      });
    }

    this.htmlElement.appendChild(this.containerElement);
  }

  /**
   * 渲染列表
   */
  public render(): void {
    if (!this.containerElement) return;

    const itemsToRender = this.config.virtualized 
      ? this.sortedItems.slice(this.visibleRange.start, this.visibleRange.end + 1)
      : this.sortedItems;

    this.containerElement.innerHTML = '';

    if (this.config.virtualized) {
      // 创建虚拟滚动容器
      const spacer = document.createElement('div');
      spacer.style.height = `${this.sortedItems.length * this.config.itemHeight}px`;
      spacer.style.position = 'relative';
      
      const content = document.createElement('div');
      content.style.position = 'absolute';
      content.style.top = `${this.visibleRange.start * this.config.itemHeight}px`;
      content.style.width = '100%';
      
      itemsToRender.forEach((item, index) => {
        const itemElement = this.createItemElement(item, this.visibleRange.start + index);
        content.appendChild(itemElement);
      });
      
      spacer.appendChild(content);
      this.containerElement.appendChild(spacer);
    } else {
      // 根据模式渲染
      if (this.config.mode === ListViewMode.GRID) {
        this.renderGridMode(itemsToRender);
      } else {
        itemsToRender.forEach((item, index) => {
          const itemElement = this.createItemElement(item, index);
          this.containerElement!.appendChild(itemElement);
        });
      }
    }
  }

  /**
   * 渲染网格模式
   */
  private renderGridMode(items: ListItem[]): void {
    const gridContainer = document.createElement('div');
    gridContainer.style.cssText = `
      display: grid;
      grid-template-columns: repeat(${this.config.gridColumns}, 1fr);
      gap: ${this.config.itemSpacing}px;
      padding: ${this.config.itemSpacing}px;
    `;

    items.forEach((item, index) => {
      const itemElement = this.createItemElement(item, index);
      gridContainer.appendChild(itemElement);
    });

    this.containerElement!.appendChild(gridContainer);
  }

  /**
   * 创建项目元素
   */
  private createItemElement(item: ListItem, index: number): HTMLElement {
    const itemElement = document.createElement('div');
    itemElement.className = `list-item ${item.className || ''}`;
    
    const baseStyle = `
      display: flex;
      align-items: center;
      padding: 12px;
      cursor: pointer;
      user-select: none;
      border-bottom: 1px solid var(--ui-color-border);
      transition: background-color 0.2s ease;
    `;

    if (this.config.mode === ListViewMode.GRID || this.config.mode === ListViewMode.CARD) {
      itemElement.style.cssText = baseStyle + `
        flex-direction: column;
        border: 1px solid var(--ui-color-border);
        border-radius: 8px;
        margin-bottom: 0;
      `;
    } else {
      itemElement.style.cssText = baseStyle + `
        height: ${item.height || this.config.itemHeight}px;
      `;
    }

    if (item.selected) {
      itemElement.style.backgroundColor = 'var(--ui-color-primary)';
      itemElement.style.color = 'white';
    }

    if (item.disabled) {
      itemElement.style.opacity = '0.5';
      itemElement.style.cursor = 'not-allowed';
    }

    // 索引
    if (this.config.showIndex) {
      const indexElement = document.createElement('span');
      indexElement.className = 'item-index';
      indexElement.textContent = String(index + 1);
      indexElement.style.cssText = `
        margin-right: 8px;
        font-size: 12px;
        color: var(--ui-color-text-secondary);
        min-width: 20px;
      `;
      itemElement.appendChild(indexElement);
    }

    // 复选框
    if (this.config.showCheckbox) {
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.checked = item.selected || false;
      checkbox.style.marginRight = '8px';
      checkbox.addEventListener('change', (e) => {
        e.stopPropagation();
        this.selectItem(item.id, checkbox.checked);
      });
      itemElement.appendChild(checkbox);
    }

    // 图标或图片
    if (item.icon || item.image) {
      const mediaElement = document.createElement('div');
      mediaElement.className = 'item-media';
      mediaElement.style.cssText = `
        width: 32px;
        height: 32px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background: var(--ui-color-surface);
      `;

      if (item.image) {
        const img = document.createElement('img');
        img.src = item.image;
        img.style.cssText = 'width: 100%; height: 100%; object-fit: cover; border-radius: 4px;';
        mediaElement.appendChild(img);
      } else if (item.icon) {
        mediaElement.textContent = item.icon;
        mediaElement.style.fontSize = '16px';
      }

      itemElement.appendChild(mediaElement);
    }

    // 内容
    const contentElement = document.createElement('div');
    contentElement.className = 'item-content';
    contentElement.style.cssText = 'flex: 1; min-width: 0;';

    // 标题
    const titleElement = document.createElement('div');
    titleElement.className = 'item-title';
    titleElement.textContent = item.title;
    titleElement.style.cssText = `
      font-weight: 500;
      margin-bottom: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    `;
    contentElement.appendChild(titleElement);

    // 副标题
    if (item.subtitle) {
      const subtitleElement = document.createElement('div');
      subtitleElement.className = 'item-subtitle';
      subtitleElement.textContent = item.subtitle;
      subtitleElement.style.cssText = `
        font-size: 12px;
        color: var(--ui-color-text-secondary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      `;
      contentElement.appendChild(subtitleElement);
    }

    // 描述
    if (item.description && (this.config.mode === ListViewMode.CARD || this.config.mode === ListViewMode.LIST)) {
      const descElement = document.createElement('div');
      descElement.className = 'item-description';
      descElement.textContent = item.description;
      descElement.style.cssText = `
        font-size: 12px;
        color: var(--ui-color-text-secondary);
        margin-top: 4px;
        line-height: 1.4;
      `;
      contentElement.appendChild(descElement);
    }

    itemElement.appendChild(contentElement);

    // 状态指示器
    if (item.status && item.status !== 'normal') {
      const statusElement = document.createElement('div');
      statusElement.className = `item-status status-${item.status}`;
      statusElement.style.cssText = `
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 8px;
        background: var(--ui-color-${item.status});
      `;
      itemElement.appendChild(statusElement);
    }

    // 事件处理
    itemElement.addEventListener('click', (e) => {
      if (!item.disabled) {
        this.selectItem(item.id, !item.selected);
        this.events.onItemClick?.(item, e);
      }
    });

    itemElement.addEventListener('dblclick', (e) => {
      if (!item.disabled) {
        this.events.onItemDoubleClick?.(item, e);
      }
    });

    itemElement.addEventListener('contextmenu', (e) => {
      if (!item.disabled) {
        this.events.onItemContextMenu?.(item, e);
      }
    });

    // 拖拽支持
    if (this.config.draggable && item.draggable !== false) {
      itemElement.draggable = true;
      
      itemElement.addEventListener('dragstart', (e) => {
        this.events.onItemDragStart?.(item, e);
      });
      
      itemElement.addEventListener('dragover', (e) => {
        e.preventDefault();
        this.events.onItemDragOver?.(item, e);
      });
    }

    return itemElement;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new UIListViewComponent({
      id: this.id,
      type: this.uiType,
      visible: this.visible,
      interactive: this.interactive,
      position: this.position instanceof Vector3 ? this.position.clone() : new Vector2(this.position.x, this.position.y),
      size: new Vector2(this.size.x, this.size.y),
      opacity: this.opacity,
      zIndex: this.zIndex,
      layoutType: this.layoutType,
      layoutParams: { ...this.layoutParams },
      backgroundColor: this.backgroundColor,
      borderColor: this.borderColor,
      borderWidth: this.borderWidth,
      borderRadius: this.borderRadius,
      padding: { ...this.padding },
      margin: { ...this.margin },
      data: { ...this.data },
      tags: [...this.tags],
      is3D: this.is3D,
      config: { ...this.config },
      items: [...this.originalItems],
      events: { ...this.events }
    });
  }
}
