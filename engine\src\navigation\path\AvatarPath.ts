/**
 * 数字人路径类
 */
import * as THREE from 'three';
import { PathPoint } from './PathPoint';
import { AvatarPathData, LoopMode, InterpolationType, PathMetadata } from '../types';
import { generateUUID } from '../../utils/UUID';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 数字人路径类
 */
export class AvatarPath extends EventEmitter {
  /** 路径ID */
  public readonly id: string;
  /** 路径名称 */
  public name: string;
  /** 关联的数字人ID */
  public avatarId: string;
  /** 路径点列表 */
  public points: PathPoint[];
  /** 循环模式 */
  public loopMode: LoopMode;
  /** 插值类型 */
  public interpolation: InterpolationType;
  /** 是否启用 */
  public enabled: boolean;
  /** 元数据 */
  public metadata: PathMetadata;
  /** 总持续时间（秒） */
  private _totalDuration: number;
  /** 路径长度（米） */
  private _totalLength: number;
  /** 是否需要重新计算 */
  private _needsRecalculation: boolean;

  /**
   * 构造函数
   * @param data 路径数据
   */
  constructor(data: Partial<AvatarPathData> = {}) {
    super();

    this.id = data.id || generateUUID();
    this.name = data.name || '新路径';
    this.avatarId = data.avatarId || '';
    this.points = data.points?.map(p => PathPoint.fromJSON(p)) || [];
    this.loopMode = data.loopMode || LoopMode.NONE;
    this.interpolation = data.interpolation || InterpolationType.LINEAR;
    this.enabled = data.enabled !== undefined ? data.enabled : true;
    this.metadata = data.metadata || {
      createdAt: new Date(),
      updatedAt: new Date(),
      creator: 'system',
      version: 1
    };

    this._totalDuration = 0;
    this._totalLength = 0;
    this._needsRecalculation = true;

    this.recalculate();
  }

  /**
   * 获取总持续时间
   */
  public get totalDuration(): number {
    if (this._needsRecalculation) {
      this.recalculate();
    }
    return this._totalDuration;
  }

  /**
   * 获取总路径长度
   */
  public get totalLength(): number {
    if (this._needsRecalculation) {
      this.recalculate();
    }
    return this._totalLength;
  }

  /**
   * 添加路径点
   * @param point 路径点
   * @param index 插入位置，默认添加到末尾
   */
  public addPoint(point: PathPoint, index?: number): void {
    if (index !== undefined && index >= 0 && index <= this.points.length) {
      this.points.splice(index, 0, point);
    } else {
      this.points.push(point);
    }

    this._needsRecalculation = true;
    this.updateMetadata();
    this.emit('pointAdded', { point, index: index || this.points.length - 1 });
  }

  /**
   * 移除路径点
   * @param index 路径点索引
   */
  public removePoint(index: number): PathPoint | null {
    if (index >= 0 && index < this.points.length) {
      const removedPoint = this.points.splice(index, 1)[0];
      this._needsRecalculation = true;
      this.updateMetadata();
      this.emit('pointRemoved', { point: removedPoint, index });
      return removedPoint;
    }
    return null;
  }

  /**
   * 移除路径点（通过ID）
   * @param pointId 路径点ID
   */
  public removePointById(pointId: string): PathPoint | null {
    const index = this.points.findIndex(p => p.id === pointId);
    return index >= 0 ? this.removePoint(index) : null;
  }

  /**
   * 获取路径点
   * @param index 路径点索引
   */
  public getPoint(index: number): PathPoint | null {
    return index >= 0 && index < this.points.length ? this.points[index] : null;
  }

  /**
   * 获取路径点（通过ID）
   * @param pointId 路径点ID
   */
  public getPointById(pointId: string): PathPoint | null {
    return this.points.find(p => p.id === pointId) || null;
  }

  /**
   * 更新路径点
   * @param index 路径点索引
   * @param point 新的路径点数据
   */
  public updatePoint(index: number, point: Partial<PathPoint>): void {
    if (index >= 0 && index < this.points.length) {
      const currentPoint = this.points[index];
      
      if (point.position) currentPoint.setPosition(point.position);
      if (point.waitTime !== undefined) currentPoint.waitTime = point.waitTime;
      if (point.speed !== undefined) currentPoint.speed = point.speed;
      if (point.animation !== undefined) currentPoint.animation = point.animation;
      if (point.lookAt !== undefined) currentPoint.setLookAt(point.lookAt);
      if (point.triggers !== undefined) {
        currentPoint.clearTriggers();
        point.triggers.forEach(trigger => currentPoint.addTrigger(trigger));
      }
      if (point.userData !== undefined) currentPoint.userData = point.userData;

      this._needsRecalculation = true;
      this.updateMetadata();
      this.emit('pointUpdated', { point: currentPoint, index });
    }
  }

  /**
   * 清空所有路径点
   */
  public clearPoints(): void {
    this.points.length = 0;
    this._needsRecalculation = true;
    this.updateMetadata();
    this.emit('pointsCleared');
  }

  /**
   * 在指定时间获取位置
   * @param time 时间（秒）
   * @returns 位置和相关信息
   */
  public getPositionAtTime(time: number): {
    position: THREE.Vector3;
    rotation: THREE.Quaternion;
    currentPointIndex: number;
    progress: number;
    speed: number;
    animation: string;
  } | null {
    if (this.points.length === 0) return null;

    // 处理循环模式
    let normalizedTime = time;
    if (this.loopMode === LoopMode.LOOP && this.totalDuration > 0) {
      normalizedTime = time % this.totalDuration;
    } else if (this.loopMode === LoopMode.PINGPONG && this.totalDuration > 0) {
      const cycleTime = this.totalDuration * 2;
      normalizedTime = time % cycleTime;
      if (normalizedTime > this.totalDuration) {
        normalizedTime = cycleTime - normalizedTime;
      }
    }

    // 限制时间范围
    normalizedTime = Math.max(0, Math.min(normalizedTime, this.totalDuration));

    // 查找当前段
    let currentTime = 0;
    for (let i = 0; i < this.points.length; i++) {
      const point = this.points[i];
      const nextPoint = this.points[i + 1];

      // 在当前点停留
      if (normalizedTime <= currentTime + point.waitTime) {
        return {
          position: point.position.clone(),
          rotation: this.calculateRotation(point, nextPoint),
          currentPointIndex: i,
          progress: normalizedTime / this.totalDuration,
          speed: point.speed,
          animation: point.animation
        };
      }

      currentTime += point.waitTime;

      // 移动到下一个点
      if (nextPoint) {
        const segmentDuration = point.distanceTo(nextPoint) / point.speed;
        if (normalizedTime <= currentTime + segmentDuration) {
          const segmentProgress = (normalizedTime - currentTime) / segmentDuration;
          const position = this.interpolatePosition(point, nextPoint, segmentProgress);
          
          return {
            position,
            rotation: this.calculateRotation(point, nextPoint),
            currentPointIndex: i,
            progress: normalizedTime / this.totalDuration,
            speed: point.speed,
            animation: point.animation
          };
        }
        currentTime += segmentDuration;
      }
    }

    // 返回最后一个点
    const lastPoint = this.points[this.points.length - 1];
    return {
      position: lastPoint.position.clone(),
      rotation: this.calculateRotation(lastPoint, null),
      currentPointIndex: this.points.length - 1,
      progress: 1.0,
      speed: lastPoint.speed,
      animation: lastPoint.animation
    };
  }

  /**
   * 插值计算位置
   * @param point1 起始点
   * @param point2 结束点
   * @param t 插值参数 (0-1)
   * @returns 插值位置
   */
  private interpolatePosition(point1: PathPoint, point2: PathPoint, t: number): THREE.Vector3 {
    switch (this.interpolation) {
      case InterpolationType.LINEAR:
        return point1.position.clone().lerp(point2.position, t);
      
      case InterpolationType.SMOOTH:
        // 使用平滑步函数
        const smoothT = t * t * (3 - 2 * t);
        return point1.position.clone().lerp(point2.position, smoothT);
      
      case InterpolationType.BEZIER:
      case InterpolationType.SPLINE:
        // 简化实现，实际应该使用控制点
        const smoothT2 = t * t * t * (t * (t * 6 - 15) + 10);
        return point1.position.clone().lerp(point2.position, smoothT2);
      
      default:
        return point1.position.clone().lerp(point2.position, t);
    }
  }

  /**
   * 计算旋转
   * @param currentPoint 当前点
   * @param nextPoint 下一个点
   * @returns 旋转四元数
   */
  private calculateRotation(currentPoint: PathPoint, nextPoint: PathPoint | null): THREE.Quaternion {
    const quaternion = new THREE.Quaternion();

    if (currentPoint.lookAt) {
      // 使用指定的朝向目标
      const up = new THREE.Vector3(0, 1, 0);
      const matrix = new THREE.Matrix4().lookAt(currentPoint.position, currentPoint.lookAt, up);
      quaternion.setFromRotationMatrix(matrix);
    } else if (nextPoint) {
      // 朝向下一个点
      const up = new THREE.Vector3(0, 1, 0);
      const matrix = new THREE.Matrix4().lookAt(currentPoint.position, nextPoint.position, up);
      quaternion.setFromRotationMatrix(matrix);
    }

    return quaternion;
  }

  /**
   * 重新计算路径数据
   */
  private recalculate(): void {
    this._totalDuration = 0;
    this._totalLength = 0;

    for (let i = 0; i < this.points.length; i++) {
      const point = this.points[i];
      
      // 添加停留时间
      this._totalDuration += point.waitTime;

      // 添加移动时间
      if (i < this.points.length - 1) {
        const nextPoint = this.points[i + 1];
        const distance = point.distanceTo(nextPoint);
        this._totalLength += distance;
        this._totalDuration += distance / point.speed;
      }
    }

    this._needsRecalculation = false;
  }

  /**
   * 更新元数据
   */
  private updateMetadata(): void {
    this.metadata.updatedAt = new Date();
    this.metadata.version++;
  }

  /**
   * 验证路径
   * @returns 验证结果
   */
  public validate(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证基本信息
    if (!this.name || this.name.trim() === '') {
      errors.push('路径名称不能为空');
    }

    if (!this.avatarId || this.avatarId.trim() === '') {
      errors.push('必须指定关联的数字人ID');
    }

    // 验证路径点
    if (this.points.length < 2) {
      errors.push('路径至少需要2个路径点');
    }

    // 验证每个路径点
    this.points.forEach((point, index) => {
      const validation = point.validate();
      if (!validation.valid) {
        errors.push(`路径点${index + 1}: ${validation.errors.join(', ')}`);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 序列化为JSON
   * @returns JSON对象
   */
  public toJSON(): AvatarPathData {
    return {
      id: this.id,
      name: this.name,
      avatarId: this.avatarId,
      points: this.points.map(p => p.toJSON()),
      loopMode: this.loopMode,
      interpolation: this.interpolation,
      totalDuration: this.totalDuration,
      enabled: this.enabled,
      metadata: this.metadata
    };
  }

  /**
   * 从JSON反序列化
   * @param json JSON对象
   * @returns 路径实例
   */
  public static fromJSON(json: AvatarPathData): AvatarPath {
    return new AvatarPath(json);
  }

  /**
   * 克隆路径
   * @param newId 新的路径ID
   * @returns 新的路径实例
   */
  public clone(newId?: string): AvatarPath {
    const clonedData = this.toJSON();
    clonedData.id = newId || generateUUID();
    clonedData.name = `${this.name} (副本)`;
    clonedData.metadata = {
      ...this.metadata,
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1
    };

    return AvatarPath.fromJSON(clonedData);
  }

  /**
   * 从数据更新路径
   * @param data 路径数据
   */
  public updateFromData(data: Partial<AvatarPathData>): void {
    if (data.name !== undefined) this.name = data.name;
    if (data.avatarId !== undefined) this.avatarId = data.avatarId;
    if (data.loopMode !== undefined) this.loopMode = data.loopMode;
    if (data.interpolation !== undefined) this.interpolation = data.interpolation;
    if (data.enabled !== undefined) this.enabled = data.enabled;

    if (data.points) {
      this.points = data.points.map(p => PathPoint.fromJSON(p));
      this._needsRecalculation = true;
    }

    this.updateMetadata();
    this.emit('pathUpdated', { path: this });
  }

  /**
   * 获取平均速度
   * @returns 平均速度
   */
  public getAverageSpeed(): number {
    if (this.points.length === 0) return 0;

    const totalSpeed = this.points.reduce((sum, point) => sum + point.speed, 0);
    return totalSpeed / this.points.length;
  }

  /**
   * 获取总距离
   * @returns 总距离
   */
  public getTotalDistance(): number {
    if (this._needsRecalculation) {
      this.recalculate();
    }
    return this._totalLength;
  }

  /**
   * 获取路径边界框
   * @returns 边界框
   */
  public getBoundingBox(): THREE.Box3 {
    const box = new THREE.Box3();

    if (this.points.length > 0) {
      this.points.forEach(point => {
        box.expandByPoint(point.position);
      });
    }

    return box;
  }

  /**
   * 优化路径（移除冗余点）
   * @param tolerance 容差值
   */
  public optimize(tolerance: number = 0.1): void {
    if (this.points.length <= 2) return;

    const optimizedPoints: PathPoint[] = [this.points[0]]; // 保留第一个点

    for (let i = 1; i < this.points.length - 1; i++) {
      const prev = this.points[i - 1];
      const current = this.points[i];
      const next = this.points[i + 1];

      // 计算当前点到前后两点连线的距离
      const line = new THREE.Line3(prev.position, next.position);
      const closestPoint = new THREE.Vector3();
      line.closestPointToPoint(current.position, true, closestPoint);
      const distance = current.position.distanceTo(closestPoint);

      // 如果距离大于容差，保留该点
      if (distance > tolerance) {
        optimizedPoints.push(current);
      }
    }

    optimizedPoints.push(this.points[this.points.length - 1]); // 保留最后一个点

    this.points = optimizedPoints;
    this._needsRecalculation = true;
    this.updateMetadata();
    this.emit('pathOptimized', { originalCount: this.points.length, optimizedCount: optimizedPoints.length });
  }

  /**
   * 反转路径
   */
  public reverse(): void {
    this.points.reverse();
    this._needsRecalculation = true;
    this.updateMetadata();
    this.emit('pathReversed');
  }
}
