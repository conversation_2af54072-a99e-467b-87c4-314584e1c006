/**
 * UIModalComponent.ts
 *
 * 模态对话框UI组件，用于显示重要信息和用户交互
 */

import { Vector2 } from 'three';
import { UIComponent, UIComponentProps, UIComponentType } from './UIComponent';

/**
 * 模态框大小枚举
 */
export enum ModalSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  EXTRA_LARGE = 'extra-large',
  FULL_SCREEN = 'full-screen',
  CUSTOM = 'custom'
}

/**
 * 模态框类型枚举
 */
export enum ModalType {
  DEFAULT = 'default',
  CONFIRM = 'confirm',
  ALERT = 'alert',
  PROMPT = 'prompt',
  CUSTOM = 'custom'
}

/**
 * 模态框按钮配置
 */
export interface ModalButton {
  text: string;
  type?: 'primary' | 'secondary' | 'danger' | 'success';
  onClick?: () => void | Promise<void>;
  disabled?: boolean;
  loading?: boolean;
}

/**
 * 模态框属性
 */
export interface UIModalComponentProps extends UIComponentProps {
  // 标题
  title?: string;
  
  // 内容
  content?: string;
  
  // HTML内容
  htmlContent?: string;
  
  // 模态框类型
  modalType?: ModalType;
  
  // 大小
  modalSize?: ModalSize;
  
  // 自定义尺寸（当size为CUSTOM时使用）
  customSize?: Vector2;
  
  // 是否显示关闭按钮
  showCloseButton?: boolean;
  
  // 是否可以通过点击遮罩关闭
  maskClosable?: boolean;
  
  // 是否可以通过ESC键关闭
  escClosable?: boolean;
  
  // 是否显示遮罩
  showMask?: boolean;
  
  // 遮罩透明度
  maskOpacity?: number;
  
  // 是否居中显示
  centered?: boolean;
  
  // 自定义位置
  customPosition?: Vector2;
  
  // 按钮配置
  buttons?: ModalButton[];
  
  // 是否可拖拽
  draggable?: boolean;
  
  // 是否可调整大小
  resizable?: boolean;
  
  // 动画持续时间
  animationDuration?: number;
  
  // 事件回调
  onShow?: () => void;
  onHide?: () => void;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
  onClose?: () => void;
}

/**
 * 模态对话框UI组件
 */
export class UIModalComponent extends UIComponent {
  // HTML元素 - 继承自基类，不需要重新声明

  // 内容属性
  title: string = '';
  content: string = '';
  htmlContent?: string;
  modalType: ModalType = ModalType.DEFAULT;
  modalSize: ModalSize = ModalSize.MEDIUM;
  customSize?: Vector2;
  
  // 显示属性
  showCloseButton: boolean = true;
  maskClosable: boolean = true;
  escClosable: boolean = true;
  showMask: boolean = true;
  maskOpacity: number = 0.5;
  centered: boolean = true;
  customPosition?: Vector2;
  buttons: ModalButton[] = [];
  draggable: boolean = false;
  resizable: boolean = false;
  animationDuration: number = 300;
  
  // 事件回调
  onShow?: () => void;
  onHide?: () => void;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
  onClose?: () => void;
  
  // 内部状态
  private isVisible: boolean = false;
  private isDragging: boolean = false;
  private dragOffset: Vector2 = new Vector2();
  
  // 内部元素
  private maskElement?: HTMLElement;
  private modalElement?: HTMLElement;
  private headerElement?: HTMLElement;
  private titleElement?: HTMLElement;
  private closeButtonElement?: HTMLElement;
  private bodyElement?: HTMLElement;
  private footerElement?: HTMLElement;

  /**
   * 构造函数
   */
  constructor(props: UIModalComponentProps = {}) {
    super({ ...props, type: UIComponentType.MODAL, visible: false });
    
    // 设置属性
    this.title = props.title ?? '';
    this.content = props.content ?? '';
    this.htmlContent = props.htmlContent;
    this.modalType = props.modalType ?? ModalType.DEFAULT;
    this.modalSize = props.modalSize ?? ModalSize.MEDIUM;
    this.customSize = props.customSize;
    this.showCloseButton = props.showCloseButton ?? true;
    this.maskClosable = props.maskClosable ?? true;
    this.escClosable = props.escClosable ?? true;
    this.showMask = props.showMask ?? true;
    this.maskOpacity = props.maskOpacity ?? 0.5;
    this.centered = props.centered ?? true;
    this.customPosition = props.customPosition;
    this.buttons = props.buttons ?? [];
    this.draggable = props.draggable ?? false;
    this.resizable = props.resizable ?? false;
    this.animationDuration = props.animationDuration ?? 300;
    this.onShow = props.onShow;
    this.onHide = props.onHide;
    this.onConfirm = props.onConfirm;
    this.onCancel = props.onCancel;
    this.onClose = props.onClose;
    
    // 根据类型设置默认按钮
    this.setupDefaultButtons();

    // 创建HTML元素
    this.createHtmlElement();

    // 创建模态框元素
    this.createModalElements();

    // 绑定事件
    this.bindEvents();
  }

  /**
   * 设置默认按钮
   */
  private setupDefaultButtons(): void {
    if (this.buttons.length > 0) return;
    
    switch (this.modalType) {
      case ModalType.CONFIRM:
        this.buttons = [
          {
            text: '取消',
            type: 'secondary',
            onClick: () => {
              if (this.onCancel) this.onCancel();
              this.hide();
            }
          },
          {
            text: '确定',
            type: 'primary',
            onClick: async () => {
              if (this.onConfirm) {
                await this.onConfirm();
              }
              this.hide();
            }
          }
        ];
        break;
        
      case ModalType.ALERT:
        this.buttons = [
          {
            text: '确定',
            type: 'primary',
            onClick: () => {
              if (this.onConfirm) this.onConfirm();
              this.hide();
            }
          }
        ];
        break;
        
      case ModalType.PROMPT:
        this.buttons = [
          {
            text: '取消',
            type: 'secondary',
            onClick: () => {
              if (this.onCancel) this.onCancel();
              this.hide();
            }
          },
          {
            text: '确定',
            type: 'primary',
            onClick: async () => {
              if (this.onConfirm) {
                await this.onConfirm();
              }
              this.hide();
            }
          }
        ];
        break;
    }
  }

  /**
   * 创建HTML元素
   */
  private createHtmlElement(): void {
    if (!this.htmlElement) {
      this.htmlElement = document.createElement('div');
      this.htmlElement.className = 'ui-modal-container';
    }
  }

  /**
   * 创建模态框HTML元素
   */
  private createModalElements(): void {
    if (!this.htmlElement) return;
    
    // 设置容器样式
    this.htmlElement.className = 'ui-modal-container';
    this.htmlElement.style.position = 'fixed';
    this.htmlElement.style.top = '0';
    this.htmlElement.style.left = '0';
    this.htmlElement.style.width = '100%';
    this.htmlElement.style.height = '100%';
    this.htmlElement.style.zIndex = '1000';
    this.htmlElement.style.display = 'none';
    
    // 创建遮罩
    if (this.showMask) {
      this.maskElement = document.createElement('div');
      this.maskElement.className = 'ui-modal-mask';
      this.htmlElement.appendChild(this.maskElement);
    }
    
    // 创建模态框主体
    this.modalElement = document.createElement('div');
    this.modalElement.className = 'ui-modal';
    this.htmlElement.appendChild(this.modalElement);
    
    // 创建头部
    if (this.title || this.showCloseButton) {
      this.createHeader();
    }
    
    // 创建主体
    this.createBody();
    
    // 创建底部
    if (this.buttons.length > 0) {
      this.createFooter();
    }
    
    // 应用样式
    this.applyModalStyles();
  }

  /**
   * 创建头部
   */
  private createHeader(): void {
    if (!this.modalElement) return;
    
    this.headerElement = document.createElement('div');
    this.headerElement.className = 'ui-modal-header';
    this.modalElement.appendChild(this.headerElement);
    
    // 标题
    if (this.title) {
      this.titleElement = document.createElement('div');
      this.titleElement.className = 'ui-modal-title';
      this.titleElement.textContent = this.title;
      this.headerElement.appendChild(this.titleElement);
    }
    
    // 关闭按钮
    if (this.showCloseButton) {
      this.closeButtonElement = document.createElement('button');
      this.closeButtonElement.className = 'ui-modal-close';
      this.closeButtonElement.innerHTML = '×';
      this.closeButtonElement.onclick = () => this.hide();
      this.headerElement.appendChild(this.closeButtonElement);
    }
  }

  /**
   * 创建主体
   */
  private createBody(): void {
    if (!this.modalElement) return;
    
    this.bodyElement = document.createElement('div');
    this.bodyElement.className = 'ui-modal-body';
    this.modalElement.appendChild(this.bodyElement);
    
    // 设置内容
    this.updateContent();
  }

  /**
   * 创建底部
   */
  private createFooter(): void {
    if (!this.modalElement) return;
    
    this.footerElement = document.createElement('div');
    this.footerElement.className = 'ui-modal-footer';
    this.modalElement.appendChild(this.footerElement);
    
    // 创建按钮
    this.updateButtons();
  }

  /**
   * 应用模态框样式
   */
  private applyModalStyles(): void {
    // 遮罩样式
    if (this.maskElement) {
      const maskStyle = this.maskElement.style;
      maskStyle.position = 'absolute';
      maskStyle.top = '0';
      maskStyle.left = '0';
      maskStyle.width = '100%';
      maskStyle.height = '100%';
      maskStyle.backgroundColor = `rgba(0, 0, 0, ${this.maskOpacity})`;
      maskStyle.transition = `opacity ${this.animationDuration}ms ease`;
    }
    
    // 模态框样式
    if (this.modalElement) {
      const modalStyle = this.modalElement.style;
      modalStyle.position = 'relative';
      modalStyle.backgroundColor = '#ffffff';
      modalStyle.borderRadius = '8px';
      modalStyle.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      modalStyle.transition = `all ${this.animationDuration}ms ease`;
      modalStyle.transform = 'scale(0.8)';
      modalStyle.opacity = '0';
      
      // 设置大小
      this.applyModalSize();
      
      // 设置位置
      this.applyModalPosition();
    }
    
    // 头部样式
    if (this.headerElement) {
      const headerStyle = this.headerElement.style;
      headerStyle.display = 'flex';
      headerStyle.justifyContent = 'space-between';
      headerStyle.alignItems = 'center';
      headerStyle.padding = '16px 24px';
      headerStyle.borderBottom = '1px solid #f0f0f0';
      
      if (this.draggable) {
        headerStyle.cursor = 'move';
      }
    }
    
    // 标题样式
    if (this.titleElement) {
      const titleStyle = this.titleElement.style;
      titleStyle.fontSize = '16px';
      titleStyle.fontWeight = 'bold';
      titleStyle.color = '#000000';
      titleStyle.margin = '0';
    }
    
    // 关闭按钮样式
    if (this.closeButtonElement) {
      const closeStyle = this.closeButtonElement.style;
      closeStyle.background = 'none';
      closeStyle.border = 'none';
      closeStyle.fontSize = '20px';
      closeStyle.cursor = 'pointer';
      closeStyle.color = '#999999';
      closeStyle.padding = '0';
      closeStyle.width = '24px';
      closeStyle.height = '24px';
      closeStyle.display = 'flex';
      closeStyle.alignItems = 'center';
      closeStyle.justifyContent = 'center';
    }
    
    // 主体样式
    if (this.bodyElement) {
      const bodyStyle = this.bodyElement.style;
      bodyStyle.padding = '24px';
      bodyStyle.fontSize = '14px';
      bodyStyle.lineHeight = '1.5';
      bodyStyle.color = '#000000';
    }
    
    // 底部样式
    if (this.footerElement) {
      const footerStyle = this.footerElement.style;
      footerStyle.display = 'flex';
      footerStyle.justifyContent = 'flex-end';
      footerStyle.gap = '8px';
      footerStyle.padding = '16px 24px';
      footerStyle.borderTop = '1px solid #f0f0f0';
    }
  }

  /**
   * 应用模态框大小
   */
  private applyModalSize(): void {
    if (!this.modalElement) return;
    
    const style = this.modalElement.style;
    
    switch (this.modalSize) {
      case ModalSize.SMALL:
        style.width = '400px';
        style.maxHeight = '300px';
        break;
      case ModalSize.MEDIUM:
        style.width = '600px';
        style.maxHeight = '500px';
        break;
      case ModalSize.LARGE:
        style.width = '800px';
        style.maxHeight = '700px';
        break;
      case ModalSize.EXTRA_LARGE:
        style.width = '1000px';
        style.maxHeight = '800px';
        break;
      case ModalSize.FULL_SCREEN:
        style.width = '100vw';
        style.height = '100vh';
        style.borderRadius = '0';
        break;
      case ModalSize.CUSTOM:
        if (this.customSize) {
          style.width = `${this.customSize.x}px`;
          style.height = `${this.customSize.y}px`;
        }
        break;
    }
  }

  /**
   * 应用模态框位置
   */
  private applyModalPosition(): void {
    if (!this.modalElement) return;
    
    const style = this.modalElement.style;
    
    if (this.customPosition) {
      style.position = 'absolute';
      style.left = `${this.customPosition.x}px`;
      style.top = `${this.customPosition.y}px`;
    } else if (this.centered) {
      style.position = 'absolute';
      style.left = '50%';
      style.top = '50%';
      style.transform = 'translate(-50%, -50%) scale(0.8)';
    }
  }

  /**
   * 更新内容
   */
  private updateContent(): void {
    if (!this.bodyElement) return;
    
    if (this.htmlContent) {
      this.bodyElement.innerHTML = this.htmlContent;
    } else {
      this.bodyElement.textContent = this.content;
    }
  }

  /**
   * 更新按钮
   */
  private updateButtons(): void {
    if (!this.footerElement) return;
    
    // 清空现有按钮
    this.footerElement.innerHTML = '';
    
    // 创建按钮
    this.buttons.forEach(buttonConfig => {
      const button = document.createElement('button');
      button.textContent = buttonConfig.text;
      button.disabled = buttonConfig.disabled || false;
      
      // 设置按钮样式
      const buttonStyle = button.style;
      buttonStyle.padding = '8px 16px';
      buttonStyle.border = '1px solid #d9d9d9';
      buttonStyle.borderRadius = '4px';
      buttonStyle.cursor = buttonConfig.disabled ? 'not-allowed' : 'pointer';
      buttonStyle.fontSize = '14px';
      
      // 根据类型设置颜色
      switch (buttonConfig.type) {
        case 'primary':
          buttonStyle.backgroundColor = '#1890ff';
          buttonStyle.color = '#ffffff';
          buttonStyle.borderColor = '#1890ff';
          break;
        case 'danger':
          buttonStyle.backgroundColor = '#ff4d4f';
          buttonStyle.color = '#ffffff';
          buttonStyle.borderColor = '#ff4d4f';
          break;
        case 'success':
          buttonStyle.backgroundColor = '#52c41a';
          buttonStyle.color = '#ffffff';
          buttonStyle.borderColor = '#52c41a';
          break;
        default:
          buttonStyle.backgroundColor = '#ffffff';
          buttonStyle.color = '#000000';
      }
      
      // 绑定点击事件
      button.onclick = async () => {
        if (buttonConfig.onClick) {
          button.disabled = true;
          buttonConfig.loading = true;
          try {
            await buttonConfig.onClick();
          } finally {
            button.disabled = buttonConfig.disabled || false;
            buttonConfig.loading = false;
          }
        }
      };
      
      this.footerElement.appendChild(button);
    });
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // ESC键关闭
    if (this.escClosable) {
      document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }
    
    // 遮罩点击关闭
    if (this.maskClosable && this.maskElement) {
      this.maskElement.addEventListener('click', () => this.hide());
    }
    
    // 拖拽功能
    if (this.draggable && this.headerElement) {
      this.headerElement.addEventListener('mousedown', this.handleDragStart.bind(this));
    }
  }

  /**
   * 处理键盘事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape' && this.isVisible) {
      this.hide();
    }
  }

  /**
   * 处理拖拽开始
   */
  private handleDragStart(event: MouseEvent): void {
    if (!this.modalElement) return;
    
    this.isDragging = true;
    const rect = this.modalElement.getBoundingClientRect();
    this.dragOffset.set(event.clientX - rect.left, event.clientY - rect.top);
    
    document.addEventListener('mousemove', this.handleDragMove.bind(this));
    document.addEventListener('mouseup', this.handleDragEnd.bind(this));
    
    event.preventDefault();
  }

  /**
   * 处理拖拽移动
   */
  private handleDragMove(event: MouseEvent): void {
    if (!this.isDragging || !this.modalElement) return;
    
    const x = event.clientX - this.dragOffset.x;
    const y = event.clientY - this.dragOffset.y;
    
    this.modalElement.style.left = `${x}px`;
    this.modalElement.style.top = `${y}px`;
    this.modalElement.style.transform = 'none';
  }

  /**
   * 处理拖拽结束
   */
  private handleDragEnd(): void {
    this.isDragging = false;
    document.removeEventListener('mousemove', this.handleDragMove.bind(this));
    document.removeEventListener('mouseup', this.handleDragEnd.bind(this));
  }

  /**
   * 显示模态框
   */
  show(): void {
    if (this.isVisible) return;
    
    this.isVisible = true;
    
    if (this.htmlElement) {
      this.htmlElement.style.display = 'block';
      
      // 触发重排以确保动画生效
      this.htmlElement.offsetHeight;
      
      // 显示动画
      if (this.maskElement) {
        this.maskElement.style.opacity = '1';
      }
      
      if (this.modalElement) {
        this.modalElement.style.opacity = '1';
        this.modalElement.style.transform = this.centered ? 
          'translate(-50%, -50%) scale(1)' : 'scale(1)';
      }
    }
    
    if (this.onShow) {
      this.onShow();
    }
  }

  /**
   * 隐藏模态框
   */
  hide(): void {
    if (!this.isVisible) return;
    
    this.isVisible = false;
    
    if (this.htmlElement) {
      // 隐藏动画
      if (this.maskElement) {
        this.maskElement.style.opacity = '0';
      }
      
      if (this.modalElement) {
        this.modalElement.style.opacity = '0';
        this.modalElement.style.transform = this.centered ? 
          'translate(-50%, -50%) scale(0.8)' : 'scale(0.8)';
      }
      
      // 动画结束后隐藏
      setTimeout(() => {
        if (this.htmlElement) {
          this.htmlElement.style.display = 'none';
        }
      }, this.animationDuration);
    }
    
    if (this.onHide) {
      this.onHide();
    }
    
    if (this.onClose) {
      this.onClose();
    }
  }

  /**
   * 设置标题
   */
  setTitle(title: string): void {
    this.title = title;
    if (this.titleElement) {
      this.titleElement.textContent = title;
    }
  }

  /**
   * 设置内容
   */
  setContent(content: string): void {
    this.content = content;
    this.htmlContent = undefined;
    this.updateContent();
  }

  /**
   * 设置HTML内容
   */
  setHTMLContent(htmlContent: string): void {
    this.htmlContent = htmlContent;
    this.updateContent();
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new UIModalComponent({
      id: this.id,
      type: this.uiType,
      visible: this.visible,
      interactive: this.interactive,
      position: this.position instanceof Vector3 ? this.position.clone() : new Vector2(this.position.x, this.position.y),
      size: new Vector2(this.size.x, this.size.y),
      opacity: this.opacity,
      zIndex: this.zIndex,
      layoutType: this.layoutType,
      layoutParams: { ...this.layoutParams },
      backgroundColor: this.backgroundColor,
      borderColor: this.borderColor,
      borderWidth: this.borderWidth,
      borderRadius: this.borderRadius,
      padding: { ...this.padding },
      margin: { ...this.margin },
      data: { ...this.data },
      tags: [...this.tags],
      is3D: this.is3D,
      title: this.title,
      content: this.content,
      htmlContent: this.htmlContent,
      modalSize: this.modalSize,
      customSize: this.customSize ? new Vector2(this.customSize.x, this.customSize.y) : undefined,
      customPosition: this.customPosition ? new Vector2(this.customPosition.x, this.customPosition.y) : undefined,
      centered: this.centered,
      closable: this.closable,
      maskClosable: this.maskClosable,
      escClosable: this.escClosable,
      draggable: this.draggable,
      resizable: this.resizable,
      animated: this.animated,
      animationDuration: this.animationDuration,
      buttons: [...this.buttons],
      onShow: this.onShow,
      onHide: this.onHide,
      onConfirm: this.onConfirm,
      onCancel: this.onCancel,
      onClose: this.onClose
    });
  }

  /**
   * 销毁组件
   */
  override dispose(): void {
    // 移除事件监听器
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
    document.removeEventListener('mousemove', this.handleDragMove.bind(this));
    document.removeEventListener('mouseup', this.handleDragEnd.bind(this));

    this.maskElement = undefined;
    this.modalElement = undefined;
    this.headerElement = undefined;
    this.titleElement = undefined;
    this.closeButtonElement = undefined;
    this.bodyElement = undefined;
    this.footerElement = undefined;
    this.onShow = undefined;
    this.onHide = undefined;
    this.onConfirm = undefined;
    this.onCancel = undefined;
    this.onClose = undefined;

    super.dispose();
  }
}
