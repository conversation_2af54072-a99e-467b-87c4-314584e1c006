/**
 * 球形区域光
 * 物理精确的球形区域光
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { AreaLight, AreaLightOptions, AreaLightType } from './AreaLight';

/**
 * 球形区域光选项接口
 */
export interface SphereAreaLightOptions extends AreaLightOptions {
  /** 光源类型 */
  type: AreaLightType.SPHERE;
  /** 半径 */
  radius?: number;
  /** 是否使用物理单位 */
  usePhysicalUnits?: boolean;
  /** 功率（瓦特） */
  power?: number;
  /** 发光效率（流明/瓦特） */
  efficacy?: number;
  /** 辅助对象细分数 */
  helperSegments?: number;
}

/**
 * 球形区域光组件类
 */
export class SphereAreaLightComponent extends AreaLight {
  /** 半径 */
  private radius: number;

  /** 是否使用物理单位 */
  private usePhysicalUnits: boolean;

  /** 功率（瓦特） */
  private power: number;

  /** 发光效率（流明/瓦特） */
  private efficacy: number;

  /** 辅助对象细分数 */
  private helperSegments: number;

  /** 辅助对象材质 */
  private helperMaterial: THREE.MeshBasicMaterial | null = null;

  /**
   * 创建球形区域光组件
   * @param options 球形区域光选项
   */
  constructor(options: SphereAreaLightOptions) {
    super(options);

    this.radius = options.radius !== undefined ? options.radius : 1;
    this.usePhysicalUnits = options.usePhysicalUnits !== undefined ? options.usePhysicalUnits : false;
    this.power = options.power !== undefined ? options.power : 60;
    this.efficacy = options.efficacy !== undefined ? options.efficacy : 80;
    this.helperSegments = options.helperSegments !== undefined ? options.helperSegments : 16;

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 创建光源
   * @param options 球形区域光选项
   * @returns Three.js点光源
   */
  protected createLight(options: SphereAreaLightOptions): THREE.PointLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const distance = 0; // 无限距离
    const decay = 2; // 物理衰减

    const light = new THREE.PointLight(color, intensity, distance, decay);

    // 设置阴影
    light.castShadow = options.castShadow !== undefined ? options.castShadow : false;

    if (light.castShadow) {
      // 设置阴影贴图大小
      light.shadow.mapSize.width = 1024;
      light.shadow.mapSize.height = 1024;

      // 设置阴影相机
      light.shadow.camera.near = 0.1;
      light.shadow.camera.far = 500;

      // 设置阴影偏移
      light.shadow.bias = -0.0005;
    }

    return light;
  }

  /**
   * 创建辅助对象
   * @returns Three.js球形区域光辅助对象
   */
  protected createHelper(): THREE.Object3D {
    // 创建球体几何体
    const geometry = new THREE.SphereGeometry(this.radius, this.helperSegments, this.helperSegments);

    // 创建材质
    this.helperMaterial = new THREE.MeshBasicMaterial({
      color: this.helperColor,
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });

    // 创建网格
    const mesh = new THREE.Mesh(geometry, this.helperMaterial);
    mesh.visible = this.showHelper;

    return mesh;
  }

  /**
   * 更新辅助对象颜色
   */
  protected updateHelperColor(): void {
    if (this.helperMaterial) {
      this.helperMaterial.color.copy(this.helperColor);
    }
  }

  /**
   * 更新物理强度
   */
  private updatePhysicalIntensity(): void {
    if (!this.usePhysicalUnits) return;

    // 计算流明
    const lumens = this.power * this.efficacy;

    // 计算表面积
    const surfaceArea = 4 * Math.PI * this.radius * this.radius;

    // 计算强度（流明/表面积）
    const intensity = lumens / surfaceArea;

    // 设置光源强度
    if (this.light instanceof THREE.PointLight) {
      this.light.intensity = intensity;
    }
  }

  /**
   * 设置半径
   * @param radius 半径
   */
  public setRadius(radius: number): void {
    this.radius = radius;

    // 更新辅助对象
    this.updateHelper();

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取半径
   * @returns 半径
   */
  public getRadius(): number {
    return this.radius;
  }

  /**
   * 设置是否使用物理单位
   * @param use 是否使用
   */
  public setUsePhysicalUnits(use: boolean): void {
    this.usePhysicalUnits = use;

    // 如果使用物理单位，则更新光源强度
    if (use) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取是否使用物理单位
   * @returns 是否使用
   */
  public isUsePhysicalUnits(): boolean {
    return this.usePhysicalUnits;
  }

  /**
   * 设置功率
   * @param power 功率（瓦特）
   */
  public setPower(power: number): void {
    this.power = power;

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取功率
   * @returns 功率（瓦特）
   */
  public getPower(): number {
    return this.power;
  }

  /**
   * 设置发光效率
   * @param efficacy 发光效率（流明/瓦特）
   */
  public setEfficacy(efficacy: number): void {
    this.efficacy = efficacy;

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取发光效率
   * @returns 发光效率（流明/瓦特）
   */
  public getEfficacy(): number {
    return this.efficacy;
  }

  /**
   * 设置辅助对象细分数
   * @param segments 细分数
   */
  public setHelperSegments(segments: number): void {
    this.helperSegments = segments;

    // 更新辅助对象
    this.updateHelper();
  }

  /**
   * 获取辅助对象细分数
   * @returns 细分数
   */
  public getHelperSegments(): number {
    return this.helperSegments;
  }

  /**
   * 更新辅助对象
   */
  private updateHelper(): void {
    if (this.helper) {
      // 更新辅助对象
      const parent = this.helper.parent;

      if (parent) {
        parent.remove(this.helper);
      }

      // 如果辅助对象有dispose方法，则调用
      if ((this.helper as any).geometry) {
        ((this.helper as any).geometry as any).dispose();
      }

      this.helper = this.createHelper();

      if (parent) {
        parent.add(this.helper);
      }
    }
  }

  /**
   * 更新组件
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 球形区域光不需要每帧更新
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    // 获取当前光源的属性
    let color = 0xffffff;
    let intensity = 1;
    let castShadow = false;

    if (this.light instanceof THREE.PointLight) {
      color = this.light.color.getHex();
      intensity = this.light.intensity;
      castShadow = this.light.castShadow;
    }

    return new SphereAreaLightComponent({
      type: AreaLightType.SPHERE,
      color: color,
      intensity: intensity,
      castShadow: castShadow,
      showHelper: this.showHelper,
      helperColor: this.helperColor.getHex(),
      radius: this.radius,
      usePhysicalUnits: this.usePhysicalUnits,
      power: this.power,
      efficacy: this.efficacy,
      helperSegments: this.helperSegments
    });
  }
}
